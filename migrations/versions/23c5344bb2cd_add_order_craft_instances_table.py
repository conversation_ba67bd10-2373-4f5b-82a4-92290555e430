"""Add order_craft_instances table

Revision ID: 23c5344bb2cd
Revises: 1dd65cadd6eb
Create Date: 2025-06-28 20:42:07.623685

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '23c5344bb2cd'
down_revision = '1dd65cadd6eb'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('order_craft_instances',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='主键ID'),
    sa.Column('factory_id', sa.Integer(), nullable=False, comment='工厂ID'),
    sa.Column('order_no', sa.String(length=100), nullable=False, comment='订单号'),
    sa.Column('order_part_no', sa.String(length=100), nullable=False, comment='订单部位号'),
    sa.Column('part_type', sa.String(length=50), nullable=False, comment='部位类型，对应PartType枚举值'),
    sa.Column('craft_code', sa.String(length=50), nullable=False, comment='工艺代码'),
    sa.Column('order_id', sa.Integer(), nullable=False, comment='订单ID'),
    sa.Column('order_part_id', sa.Integer(), nullable=False, comment='订单部位ID'),
    sa.Column('order_craft_id', sa.Integer(), nullable=False, comment='订单工艺ID'),
    sa.Column('status', sa.Enum('PENDING', 'IN_PROGRESS', 'COMPLETED', 'ON_HOLD', 'SKIPPED', 'FAILED', name='ordercraftinstancestatus'), nullable=False, comment='实例状态'),
    sa.Column('progress_percentage', sa.Integer(), nullable=False, comment='完成百分比 0-100'),
    sa.Column('sequence_order', sa.Integer(), nullable=False, comment='在部位工艺流程中的顺序'),
    sa.Column('is_required', sa.Boolean(), nullable=False, comment='是否必需的工艺步骤'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否激活'),
    sa.Column('planned_start_date', sa.DateTime(), nullable=True, comment='计划开始时间'),
    sa.Column('planned_end_date', sa.DateTime(), nullable=True, comment='计划完成时间'),
    sa.Column('actual_start_date', sa.DateTime(), nullable=True, comment='实际开始时间'),
    sa.Column('actual_end_date', sa.DateTime(), nullable=True, comment='实际完成时间'),
    sa.Column('quality_score', sa.Integer(), nullable=True, comment='质量分数 0-100'),
    sa.Column('rework_count', sa.Integer(), nullable=False, comment='返工次数'),
    sa.Column('defect_count', sa.Integer(), nullable=False, comment='次品数量'),
    sa.Column('assigned_user_id', sa.Integer(), nullable=True, comment='分配的用户ID'),
    sa.Column('supervisor_user_id', sa.Integer(), nullable=True, comment='负责人用户ID'),
    sa.Column('estimated_duration_minutes', sa.Integer(), nullable=True, comment='预估耗时(分钟)'),
    sa.Column('actual_duration_minutes', sa.Integer(), nullable=True, comment='实际耗时(分钟)'),
    sa.Column('notes', sa.Text(), nullable=True, comment='备注'),
    sa.Column('completion_notes', sa.Text(), nullable=True, comment='完成备注'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['assigned_user_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['factory_id'], ['factories.id'], ),
    sa.ForeignKeyConstraint(['order_craft_id'], ['order_crafts.id'], ),
    sa.ForeignKeyConstraint(['order_id'], ['orders.id'], ),
    sa.ForeignKeyConstraint(['order_part_id'], ['order_parts.id'], ),
    sa.ForeignKeyConstraint(['supervisor_user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('order_no', 'order_part_no', 'craft_code', 'factory_id', name='uq_order_craft_instance_part_craft'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index(op.f('ix_order_craft_instances_craft_code'), 'order_craft_instances', ['craft_code'], unique=False)
    op.create_index(op.f('ix_order_craft_instances_factory_id'), 'order_craft_instances', ['factory_id'], unique=False)
    op.create_index(op.f('ix_order_craft_instances_order_craft_id'), 'order_craft_instances', ['order_craft_id'], unique=False)
    op.create_index(op.f('ix_order_craft_instances_order_id'), 'order_craft_instances', ['order_id'], unique=False)
    op.create_index(op.f('ix_order_craft_instances_order_no'), 'order_craft_instances', ['order_no'], unique=False)
    op.create_index(op.f('ix_order_craft_instances_order_part_id'), 'order_craft_instances', ['order_part_id'], unique=False)
    op.create_index(op.f('ix_order_craft_instances_order_part_no'), 'order_craft_instances', ['order_part_no'], unique=False)
    op.create_index(op.f('ix_order_craft_instances_part_type'), 'order_craft_instances', ['part_type'], unique=False)
    op.create_index(op.f('ix_order_craft_instances_status'), 'order_craft_instances', ['status'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_order_craft_instances_status'), table_name='order_craft_instances')
    op.drop_index(op.f('ix_order_craft_instances_part_type'), table_name='order_craft_instances')
    op.drop_index(op.f('ix_order_craft_instances_order_part_no'), table_name='order_craft_instances')
    op.drop_index(op.f('ix_order_craft_instances_order_part_id'), table_name='order_craft_instances')
    op.drop_index(op.f('ix_order_craft_instances_order_no'), table_name='order_craft_instances')
    op.drop_index(op.f('ix_order_craft_instances_order_id'), table_name='order_craft_instances')
    op.drop_index(op.f('ix_order_craft_instances_order_craft_id'), table_name='order_craft_instances')
    op.drop_index(op.f('ix_order_craft_instances_factory_id'), table_name='order_craft_instances')
    op.drop_index(op.f('ix_order_craft_instances_craft_code'), table_name='order_craft_instances')
    op.drop_table('order_craft_instances')
    # ### end Alembic commands ###