"""Add part_type fields to order_craft and order_craft_route tables if they exist

Revision ID: 1dd65cadd6eb
Revises: 2420d90045e3
Create Date: 2025-06-28 20:26:25.624234

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy import text

# revision identifiers, used by Alembic.
revision = '1dd65cadd6eb'
down_revision = '2420d90045e3'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Check if order_crafts table exists before adding column
    connection = op.get_bind()
    
    # Check if order_crafts table exists
    result = connection.execute(text("SHOW TABLES LIKE 'order_crafts'"))
    if result.fetchone():
        # Check if part_type column already exists
        result = connection.execute(text("SHOW COLUMNS FROM order_crafts LIKE 'part_type'"))
        if not result.fetchone():
            op.add_column('order_crafts', sa.Column('part_type', sa.String(50), nullable=True, comment='部位类型，对应PartType枚举值'))
            op.create_index('ix_order_crafts_part_type', 'order_crafts', ['part_type'])
    
    # Check if order_craft_routes table exists
    result = connection.execute(text("SHOW TABLES LIKE 'order_craft_routes'"))
    if result.fetchone():
        # Check if part_type column already exists
        result = connection.execute(text("SHOW COLUMNS FROM order_craft_routes LIKE 'part_type'"))
        if not result.fetchone():
            op.add_column('order_craft_routes', sa.Column('part_type', sa.String(50), nullable=True, comment='部位类型，对应PartType枚举值'))
            op.create_index('ix_order_craft_routes_part_type', 'order_craft_routes', ['part_type'])


def downgrade() -> None:
    # Remove the part_type columns and indexes if they exist
    connection = op.get_bind()
    
    # Check if order_crafts table exists
    result = connection.execute(text("SHOW TABLES LIKE 'order_crafts'"))
    if result.fetchone():
        # Check if part_type column exists
        result = connection.execute(text("SHOW COLUMNS FROM order_crafts LIKE 'part_type'"))
        if result.fetchone():
            try:
                op.drop_index('ix_order_crafts_part_type', 'order_crafts')
            except:
                pass  # Index might not exist
            op.drop_column('order_crafts', 'part_type')
    
    # Check if order_craft_routes table exists
    result = connection.execute(text("SHOW TABLES LIKE 'order_craft_routes'"))
    if result.fetchone():
        # Check if part_type column exists
        result = connection.execute(text("SHOW COLUMNS FROM order_craft_routes LIKE 'part_type'"))
        if result.fetchone():
            try:
                op.drop_index('ix_order_craft_routes_part_type', 'order_craft_routes')
            except:
                pass  # Index might not exist
            op.drop_column('order_craft_routes', 'part_type')