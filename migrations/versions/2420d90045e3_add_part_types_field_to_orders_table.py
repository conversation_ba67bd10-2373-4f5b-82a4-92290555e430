"""Add part_types field to orders table

Revision ID: 2420d90045e3
Revises: f2cf93ce02a1
Create Date: 2025-06-28 20:06:43.689989

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '2420d90045e3'
down_revision = 'f2cf93ce02a1'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('orders', sa.Column('part_types', sa.JSON(), nullable=True, comment='订单所需的部位类型列表'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('orders', 'part_types')
    # ### end Alembic commands ###