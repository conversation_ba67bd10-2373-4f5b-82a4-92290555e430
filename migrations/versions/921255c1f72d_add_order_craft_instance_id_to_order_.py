"""Add order_craft_instance_id to order_craft_route_instances

Revision ID: 921255c1f72d
Revises: 23c5344bb2cd
Create Date: 2025-06-28 20:57:32.123456

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '921255c1f72d'
down_revision = '23c5344bb2cd'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Check if order_craft_route_instances table exists before adding column
    try:
        op.add_column('order_craft_route_instances', 
            sa.Column('order_craft_instance_id', sa.Integer(), nullable=True, comment='订单工艺实例ID'))
        op.create_index(op.f('ix_order_craft_route_instances_order_craft_instance_id'), 
                       'order_craft_route_instances', ['order_craft_instance_id'], unique=False)
        op.create_foreign_key('fk_order_craft_route_instances_craft_instance', 
                             'order_craft_route_instances', 'order_craft_instances', 
                             ['order_craft_instance_id'], ['id'])
    except Exception:
        # Table might not exist, skip this migration
        pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    try:
        op.drop_constraint('fk_order_craft_route_instances_craft_instance', 'order_craft_route_instances', type_='foreignkey')
        op.drop_index(op.f('ix_order_craft_route_instances_order_craft_instance_id'), table_name='order_craft_route_instances')
        op.drop_column('order_craft_route_instances', 'order_craft_instance_id')
    except Exception:
        # Table might not exist, skip this migration
        pass
    # ### end Alembic commands ###