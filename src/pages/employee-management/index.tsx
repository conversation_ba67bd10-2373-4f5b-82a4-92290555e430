import { useState, useEffect } from 'react'
import { View, Text, Button, Input, ScrollView } from '@tarojs/components'
import Taro from '@tarojs/taro'
import ApiService from '../../services/api'
import './index.scss'

interface FactoryUser {
  user: {
    id: number
    username: string
    email: string
    full_name: string | null
    is_active: boolean
    avatar_url: string | null
    created_at: string
  }
  factory_role: string
  factory_status: string
  joined_at: string | null
  skills: Array<{
    skill_name: string
    proficiency_level: string
  }>
  skills_count: number
}

interface EmployeeListData {
  users: FactoryUser[]
  total: number
  factory_id: number
  factory_name: string
}

interface Department {
  id: number
  name: string
  description: string | null
  manager_id: number | null
  manager_name: string | null
  employee_count: number
  is_active: boolean
  created_at: string
  updated_at: string
}

interface Role {
  id: string
  name: string
  display_name: string
  description: string | null
  permissions: string[]
  is_active: boolean
}

const EmployeeManagement = () => {
  const [employees, setEmployees] = useState<EmployeeListData | null>(null)
  const [departments, setDepartments] = useState<Department[]>([])
  const [roles, setRoles] = useState<Role[]>([])
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedRole, setSelectedRole] = useState<string>('')
  const [selectedDepartment, setSelectedDepartment] = useState<string>('')

  useEffect(() => {
    loadEmployees()
    loadDepartments()
    loadRoles()
  }, [])

  const loadEmployees = async () => {
    setLoading(true)
    try {
      const data = await ApiService.getEmployeeList()
      setEmployees(data)
    } catch (error) {
      console.error('获取员工列表失败:', error)
      Taro.showToast({
        title: '加载失败',
        icon: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  const loadDepartments = async () => {
    try {
      const data = await ApiService.getDepartmentList()
      setDepartments(data)
    } catch (error) {
      console.error('获取部门列表失败:', error)
    }
  }

  const loadRoles = async () => {
    try {
      const data = await ApiService.getRoleList()
      setRoles(data?.roles || [])
    } catch (error) {
      console.error('获取角色列表失败:', error)
    }
  }

  const handleSearch = async () => {
    if (!searchTerm.trim()) {
      await loadEmployees()
      return
    }

    setLoading(true)
    try {
      const availableUsers = await ApiService.searchAvailableUsers({
        search_term: searchTerm,
        role_id: selectedRole ? parseInt(selectedRole) : undefined
      })

      Taro.showToast({
        title: `找到 ${availableUsers.users.length} 个结果`,
        icon: 'none'
      })
    } catch (error) {
      console.error('搜索失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleEmployeeAction = (_action: string, employee: FactoryUser) => {
    Taro.showActionSheet({
      itemList: ['查看详情', '编辑信息', '暂停用户', '移除用户'],
      success: async (res) => {
        switch (res.tapIndex) {
          case 0:
            showEmployeeDetails(employee)
            break
          case 1:
            editEmployee(employee)
            break
          case 2:
            await suspendEmployee(employee)
            break
          case 3:
            await removeEmployee(employee)
            break
        }
      }
    })
  }

  const showEmployeeDetails = (employee: FactoryUser) => {
    const { user, factory_role, skills } = employee
    const skillsText = skills.map(s => `${s.skill_name}(${s.proficiency_level})`).join(', ')

    Taro.showModal({
      title: '员工详情',
      content: `姓名: ${user.full_name || user.username}\n邮箱: ${user.email}\n角色: ${factory_role}\n技能: ${skillsText || '无'}`,
      showCancel: false
    })
  }

  const editEmployee = (employee: FactoryUser) => {
    Taro.navigateTo({
      url: `/pages/employee-edit/index?id=${employee.user.id}`
    })
  }

  const suspendEmployee = async (employee: FactoryUser) => {
    const confirmed = await new Promise<boolean>((resolve) => {
      Taro.showModal({
        title: '确认暂停',
        content: `确定要暂停员工 ${employee.user.full_name || employee.user.username} 吗？`,
        success: (res) => resolve(res.confirm)
      })
    })

    if (!confirmed) return

    try {
      await ApiService.suspendEmployee({ user_id: employee.user.id })
      Taro.showToast({
        title: '暂停成功',
        icon: 'success'
      })
      await loadEmployees()
    } catch (error) {
      console.error('暂停失败:', error)
      Taro.showToast({
        title: '暂停失败',
        icon: 'error'
      })
    }
  }

  const removeEmployee = async (employee: FactoryUser) => {
    const confirmed = await new Promise<boolean>((resolve) => {
      Taro.showModal({
        title: '确认移除',
        content: `确定要从工厂移除员工 ${employee.user.full_name || employee.user.username} 吗？`,
        success: (res) => resolve(res.confirm)
      })
    })

    if (!confirmed) return

    try {
      await ApiService.removeEmployeeFromFactory({ user_id: employee.user.id })
      Taro.showToast({
        title: '移除成功',
        icon: 'success'
      })
      await loadEmployees()
    } catch (error) {
      console.error('移除失败:', error)
      Taro.showToast({
        title: '移除失败',
        icon: 'error'
      })
    }
  }

  const handleAddEmployee = () => {
    Taro.navigateTo({
      url: '/pages/employee-add/index'
    })
  }

  const handleInviteUser = () => {
    Taro.showActionSheet({
      itemList: ['邀请单个用户', '批量邀请用户'],
      success: (res) => {
        if (res.tapIndex === 0) {
          showInviteUserModal()
        } else if (res.tapIndex === 1) {
          showBatchInviteModal()
        }
      }
    })
  }

  const showInviteUserModal = () => {
    Taro.navigateTo({
      url: '/pages/invite-user/index'
    })
  }

  const showBatchInviteModal = () => {
    Taro.navigateTo({
      url: '/pages/batch-invite/index'
    })
  }

  const getRoleDisplayName = (role: string) => {
    const roleData = roles.find(r => r.id === role || r.name === role)
    return roleData ? roleData.display_name : role
  }

  const getStatusColor = (status: string, isActive: boolean) => {
    if (!isActive) return 'status-inactive'
    if (status === 'SUSPENDED') return 'status-suspended'
    return 'status-active'
  }

  const getDepartmentByRole = (role: string) => {
    // This function maps roles to departments - you might want to adjust this logic
    // based on your backend data structure. For now, keeping the original logic
    // but you could potentially store department_id in the employee data instead
    const roleToDepartmentMap = {
      WORKER: 'PRODUCTION',
      SUPERVISOR: 'QUALITY',
      MANAGER: 'ADMIN_DEPT',
      ADMIN: 'ADMIN_DEPT'
    }
    return roleToDepartmentMap[role] || 'PRODUCTION'
  }

  const filteredEmployees = employees?.users.filter(emp => {
    const matchesSearch = !searchTerm ||
      emp.user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      emp.user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (emp.user.full_name && emp.user.full_name.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesRole = !selectedRole || emp.factory_role === selectedRole

    // For department filtering, we need to check if the employee belongs to the selected department
    // Since the employee data doesn't directly contain department_id, we'll use the role-to-department mapping for now
    // You may want to modify this based on your actual data structure
    let matchesDepartment = true
    if (selectedDepartment) {
      const empDepartmentKey = getDepartmentByRole(emp.factory_role)
      // Map department keys to department IDs - this is a temporary solution
      // Ideally, the employee data should include department_id directly
      const departmentKeyToIdMap = {
        PRODUCTION: departments.find(d => d.name === '生产部')?.id,
        QUALITY: departments.find(d => d.name === '质检部')?.id,
        WAREHOUSE: departments.find(d => d.name === '仓储部')?.id,
        MAINTENANCE: departments.find(d => d.name === '维修部')?.id,
        ADMIN_DEPT: departments.find(d => d.name === '行政部')?.id
      }
      const empDepartmentId = departmentKeyToIdMap[empDepartmentKey]
      matchesDepartment = empDepartmentId?.toString() === selectedDepartment
    }

    return matchesSearch && matchesRole && matchesDepartment
  }) || []

  return (
    <View className='employee-management'>
      <View className='header-section'>
        <View className='factory-info'>
          <Text className='factory-name'>{employees?.factory_name || '示例服装厂'}</Text>
          <Text className='employee-count'>共 {employees?.total || 0} 名员工</Text>
        </View>
        <View className='action-buttons'>
          <Button className='invite-btn' onClick={handleInviteUser}>
            邀请用户
          </Button>
          <Button className='add-btn' onClick={handleAddEmployee}>
            添加员工
          </Button>
        </View>
      </View>

      <View className='search-section'>
        <View className='search-row'>
          <Input
            className='search-input'
            placeholder='搜索员工姓名、用户名或邮箱'
            value={searchTerm}
            onInput={(e) => setSearchTerm(e.detail.value)}
          />
          <Button className='search-btn' onClick={handleSearch}>
            搜索
          </Button>
        </View>

        <View className='filter-row'>
          <Text className='filter-label'>角色:</Text>
          <View className='role-filters'>
            <Text
              className={`filter-item ${!selectedRole ? 'active' : ''}`}
              onClick={() => setSelectedRole('')}
            >
              全部
            </Text>
            {roles?.filter?.(role => role.is_active).map(role => (
              <Text
                key={role.id}
                className={`filter-item ${selectedRole === role.id ? 'active' : ''}`}
                onClick={() => setSelectedRole(role.id)}
              >
                {role.display_name}
              </Text>
            ))}
          </View>
        </View>

        <View className='filter-row'>
          <Text className='filter-label'>部门:</Text>
          <View className='role-filters'>
            <Text
              className={`filter-item ${!selectedDepartment ? 'active' : ''}`}
              onClick={() => setSelectedDepartment('')}
            >
              全部
            </Text>
            {departments?.filter?.(dept => dept.is_active).map(department => (
              <Text
                key={department.id}
                className={`filter-item ${selectedDepartment === department.id.toString() ? 'active' : ''}`}
                onClick={() => setSelectedDepartment(department.id.toString())}
              >
                {department.name}
              </Text>
            )) || []}
          </View>
        </View>
      </View>

      <ScrollView className='employee-list' scrollY>
        {loading ? (
          <View className='loading-container'>
            <Text>加载中...</Text>
          </View>
        ) : filteredEmployees.length === 0 ? (
          <View className='empty-container'>
            <Text>暂无员工数据</Text>
          </View>
        ) : (
          filteredEmployees.map((employee) => (
            <View
              key={employee.user.id}
              className='employee-item'
              onClick={() => handleEmployeeAction('menu', employee)}
            >
              <View className='employee-avatar'>
                <Text className='avatar-text'>
                  {(employee.user.full_name || employee.user.username).charAt(0).toUpperCase()}
                </Text>
              </View>

              <View className='employee-info'>
                <View className='name-row'>
                  <Text className='employee-name'>
                    {employee.user.full_name || employee.user.username}
                  </Text>
                  <View className={`status-badge ${getStatusColor(employee.factory_status, employee.user.is_active)}`}>
                    <Text className='status-text'>
                      {!employee.user.is_active ? '已停用' :
                       employee.factory_status === 'SUSPENDED' ? '已暂停' : '正常'}
                    </Text>
                  </View>
                </View>

                <Text className='employee-email'>{employee.user.email}</Text>

                <View className='role-skills-row'>
                  <Text className='employee-role'>{getRoleDisplayName(employee.factory_role)}</Text>
                  <Text className='skills-count'>技能: {employee.skills_count}</Text>
                </View>

                {employee.joined_at && (
                  <Text className='join-date'>
                    入职: {new Date(employee.joined_at).toLocaleDateString()}
                  </Text>
                )}
              </View>
            </View>
          ))
        )}
      </ScrollView>
    </View>
  )
}

export default EmployeeManagement
