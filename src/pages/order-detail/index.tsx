/* eslint-disable jsx-quotes */
import { useState, useEffect, useCallback } from 'react'
import { View, Text, ScrollView } from '@tarojs/components'
import Taro from '@tarojs/taro'
import ApiService from '../../services/api'
import StatusBadge from '../../components/StatusBadge'
import ProgressBar from '../../components/ProgressBar'
import { formatDate, formatCurrency, formatNumber, getProgressColor } from '../../utils/orderUtils'
import './index.scss'

interface OrderDetail {
  id: number
  order_no: string
  external_order_no: string
  skc_no: string
  factory_id: number
  factory_name: string
  owner_user_id: number
  owner_name: string
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'ON_HOLD' | 'DELAYED'
  total_amount: number
  cost: number
  price: number
  current_craft: string
  created_at: string
  started_at: string | null
  expect_finished_at: string | null
  finished_at: string | null
  completion_rate: number
  production_progress: number
}

interface OrderLine {
  id: number
  order_id: number
  order_line_no: string
  size: string
  amount: number
  produced_amount: number
  completed_amount: number
  current_craft_route_code: string
  production_progress: number
  completion_progress: number
  remaining_quantity: number
}

interface OrderCraft {
  id: number
  order_no: string
  craft_code: string
  craft_name: string
  order: number
  status: 'pending' | 'in_progress' | 'completed' | 'skipped'
  is_required: boolean
  is_active: boolean
  estimated_duration_hours: number
  actual_duration_hours: number
  start_time: string | null
  end_time: string | null
  routes: Array<{
    id: number
    order_craft_id: number
    skill_code: string
    skill_name: string
    order: number
    assigned_user_id: number | null
    assigned_user_name: string | null
    price: number
    total_cost: number
    quality_score: number | null
    measurement_types: string[]
    registration_types: string[]
    measurement_data: any
    registration_data: any
  }>
}

const OrderDetail = () => {
  const [orderDetail, setOrderDetail] = useState<OrderDetail | null>(null)
  const [orderLines, setOrderLines] = useState<OrderLine[]>([])
  const [orderCrafts, setOrderCrafts] = useState<OrderCraft[]>([])
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState<'summary' | 'sizes' | 'workflow' | 'operations'>('summary')

  const loadOrderDetail = useCallback(async (orderNumber: string) => {
    setLoading(true)
    try {
      const [detail, lines, crafts] = await Promise.all([
        ApiService.getOrderDetailByOrderNo(orderNumber),
        ApiService.getOrderLinesByOrderNo(orderNumber),
        ApiService.getOrderCraftsByOrderNo(orderNumber)
      ])

      setOrderDetail(detail)
      setOrderLines(lines)
      setOrderCrafts(crafts)
    } catch (error) {
      console.error('获取订单详情失败:', error)
      Taro.showToast({
        title: '获取订单详情失败',
        icon: 'none'
      })
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    const router = Taro.getCurrentInstance().router
    const orderNoParam = router?.params?.orderNo
    if (orderNoParam) {
      loadOrderDetail(orderNoParam)
    }
  }, [loadOrderDetail])

  const renderSummarySection = () => {
    if (!orderDetail) return null

    return (
      <View className="detail-section">
        <View className="section-header">
          <Text className="section-title">订单概览</Text>
        </View>

        <View className="summary-card">
          <View className="summary-header">
            <View className="order-title">
              <Text className="order-no">{orderDetail.order_no}</Text>
              <StatusBadge status={orderDetail.status} />
            </View>
            <Text className="created-date">创建于 {formatDate(orderDetail.created_at)}</Text>
          </View>

          <View className="summary-content">
            <View className="info-grid">
              <View className="info-item">
                <Text className="info-label">SKU编号</Text>
                <Text className="info-value">{orderDetail.skc_no}</Text>
              </View>
              {orderDetail.external_order_no && (
                <View className="info-item">
                  <Text className="info-label">客户订单号</Text>
                  <Text className="info-value">{orderDetail.external_order_no}</Text>
                </View>
              )}
              <View className="info-item">
                <Text className="info-label">工厂</Text>
                <Text className="info-value">{orderDetail.factory_name}</Text>
              </View>
              <View className="info-item">
                <Text className="info-label">负责人</Text>
                <Text className="info-value">{orderDetail.owner_name}</Text>
              </View>
              <View className="info-item">
                <Text className="info-label">总数量</Text>
                <Text className="info-value">{formatNumber(orderDetail.total_amount)}</Text>
              </View>
              <View className="info-item">
                <Text className="info-label">订单金额</Text>
                <Text className="info-value">{formatCurrency(orderDetail.price)}</Text>
              </View>
            </View>

            <View className="progress-section">
              <ProgressBar
                percentage={orderDetail.completion_rate}
                label="完成进度"
                color={getProgressColor(orderDetail.completion_rate)}
              />
              <ProgressBar
                percentage={orderDetail.production_progress}
                label="生产进度"
                color={getProgressColor(orderDetail.production_progress)}
              />
            </View>

            <View className="timeline-section">
              <View className="timeline-item">
                <Text className="timeline-label">开始时间:</Text>
                <Text className="timeline-value">{formatDate(orderDetail.started_at)}</Text>
              </View>
              <View className="timeline-item">
                <Text className="timeline-label">预期完成:</Text>
                <Text className="timeline-value">{formatDate(orderDetail.expect_finished_at)}</Text>
              </View>
              {orderDetail.finished_at && (
                <View className="timeline-item">
                  <Text className="timeline-label">实际完成:</Text>
                  <Text className="timeline-value">{formatDate(orderDetail.finished_at)}</Text>
                </View>
              )}
            </View>
          </View>
        </View>
      </View>
    )
  }

  const renderSizesSection = () => (
    <View className="detail-section">
      <View className="section-header">
        <Text className="section-title">尺码明细</Text>
      </View>

      <View className="sizes-list">
        {orderLines.map(line => (
          <View key={line.id} className="size-card">
            <View className="size-header">
              <Text className="size-name">{line.size}</Text>
              <Text className="size-code">{line.order_line_no}</Text>
            </View>

            <View className="size-content">
              <View className="quantity-info">
                <View className="quantity-item">
                  <Text className="quantity-label">总数量</Text>
                  <Text className="quantity-value">{formatNumber(line.amount)}</Text>
                </View>
                <View className="quantity-item">
                  <Text className="quantity-label">已生产</Text>
                  <Text className="quantity-value">{formatNumber(line.produced_amount)}</Text>
                </View>
                <View className="quantity-item">
                  <Text className="quantity-label">已完成</Text>
                  <Text className="quantity-value">{formatNumber(line.completed_amount)}</Text>
                </View>
                <View className="quantity-item">
                  <Text className="quantity-label">剩余</Text>
                  <Text className="quantity-value">{formatNumber(line.remaining_quantity)}</Text>
                </View>
              </View>

              <View className="progress-info">
                <ProgressBar
                  percentage={line.production_progress}
                  label="生产进度"
                  size="small"
                />
                <ProgressBar
                  percentage={line.completion_progress}
                  label="完成进度"
                  size="small"
                />
              </View>

              {line.current_craft_route_code && (
                <View className="current-stage">
                  <Text className="stage-label">当前阶段:</Text>
                  <Text className="stage-value">{line.current_craft_route_code}</Text>
                </View>
              )}
            </View>
          </View>
        ))}
      </View>
    </View>
  )

  const renderWorkflowSection = () => (
    <View className="detail-section">
      <View className="section-header">
        <Text className="section-title">工艺流程</Text>
      </View>

      <View className="workflow-timeline">
        {orderCrafts.map((craft, index) => (
          <View key={craft.id} className="workflow-item">
            <View className="workflow-indicator">
              <View className={`workflow-dot ${craft.status}`} />
              {index < orderCrafts.length - 1 && <View className="workflow-line" />}
            </View>

            <View className="workflow-content">
              <View className="craft-header">
                <Text className="craft-name">{craft.craft_name}</Text>
                <StatusBadge status={craft.status} size="small" />
              </View>

              <View className="craft-info">
                <Text className="craft-code">工艺代码: {craft.craft_code}</Text>
                {craft.is_required && (
                  <Text className="required-badge">必需</Text>
                )}
              </View>

              <View className="time-info">
                <Text className="time-item">
                  预计: {craft.estimated_duration_hours}小时
                </Text>
                {craft.actual_duration_hours > 0 && (
                  <Text className="time-item">
                    实际: {craft.actual_duration_hours}小时
                  </Text>
                )}
              </View>

              {craft.start_time && (
                <View className="time-range">
                  <Text className="time-label">开始:</Text>
                  <Text className="time-value">{formatDate(craft.start_time, 'datetime')}</Text>
                </View>
              )}

              {craft.end_time && (
                <View className="time-range">
                  <Text className="time-label">结束:</Text>
                  <Text className="time-value">{formatDate(craft.end_time, 'datetime')}</Text>
                </View>
              )}
            </View>
          </View>
        ))}
      </View>
    </View>
  )

  if (loading) {
    return (
      <View className="loading-container">
        <Text>加载中...</Text>
      </View>
    )
  }

  if (!orderDetail) {
    return (
      <View className="error-container">
        <Text>订单不存在</Text>
      </View>
    )
  }

  return (
    <View className="order-detail-container">
      <View className="tabs-header">
        <View className="tabs">
          <View
            className={`tab ${activeTab === 'summary' ? 'active' : ''}`}
            onClick={() => setActiveTab('summary')}
          >
            <Text className="tab-text">概览</Text>
          </View>
          <View
            className={`tab ${activeTab === 'sizes' ? 'active' : ''}`}
            onClick={() => setActiveTab('sizes')}
          >
            <Text className="tab-text">尺码</Text>
          </View>
          <View
            className={`tab ${activeTab === 'workflow' ? 'active' : ''}`}
            onClick={() => setActiveTab('workflow')}
          >
            <Text className="tab-text">工艺</Text>
          </View>
        </View>
      </View>

      <ScrollView className="detail-content" scrollY>
        {activeTab === 'summary' && renderSummarySection()}
        {activeTab === 'sizes' && renderSizesSection()}
        {activeTab === 'workflow' && renderWorkflowSection()}
      </ScrollView>
    </View>
  )
}

export default OrderDetail
