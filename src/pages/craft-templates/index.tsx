/* eslint-disable jsx-quotes */
import { useState, useEffect } from 'react'
import { View, Text, Input, Button, ScrollView } from '@tarojs/components'
import Taro from '@tarojs/taro'
import ApiService from '../../services/api'
import './index.scss'

interface CraftTemplate {
  id: number
  code: string
  name: string
  part_type: string
  description?: string
  crafts_count: number
  created_at: string
  updated_at: string
}

interface FilterState {
  search: string
  part_type: string
  sortBy: string
  sortOrder: 'asc' | 'desc'
}

const CraftTemplates = () => {
  const [templates, setTemplates] = useState<CraftTemplate[]>([])
  const [filteredTemplates, setFilteredTemplates] = useState<CraftTemplate[]>([])
  const [loading, setLoading] = useState(false)
  const [refreshing, setRefreshing] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [useSampleData, setUseSampleData] = useState(true) // Toggle for demo
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    part_type: '',
    sortBy: 'created_at',
    sortOrder: 'desc'
  })
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  })

  useEffect(() => {
    loadTemplates()
  }, [])

  useEffect(() => {
    applyFiltersAndSort()
  }, [templates, filters])

  const loadTemplates = async (page = 1, isRefresh = false) => {
    if (isRefresh) {
      setRefreshing(true)
    } else {
      setLoading(true)
    }

    try {
      // For demo purposes, use sample data or empty array
      const sampleTemplates: CraftTemplate[] = useSampleData ? [
        {
          id: 1,
          code: 'TPL001',
          name: '男士T恤工艺模板',
          part_type: 'FRONT_BODY',
          description: '标准男士T恤前身工艺流程模板',
          crafts_count: 5,
          created_at: '2024-01-15T08:30:00Z',
          updated_at: '2024-01-20T14:22:00Z'
        },
        {
          id: 2,
          code: 'TPL002',
          name: '女士连衣裙袖子模板',
          part_type: 'SLEEVE',
          description: '女士连衣裙袖子部分的工艺模板',
          crafts_count: 3,
          created_at: '2024-01-16T09:15:00Z',
          updated_at: '2024-01-18T16:45:00Z'
        },
        {
          id: 3,
          code: 'TPL003',
          name: '衬衫领子工艺模板',
          part_type: 'COLLAR',
          description: '标准衬衫领子制作工艺模板',
          crafts_count: 4,
          created_at: '2024-01-17T10:00:00Z',
          updated_at: '2024-01-19T11:30:00Z'
        },
        {
          id: 4,
          code: 'TPL004',
          name: '裤子后身工艺模板',
          part_type: 'BACK_BODY',
          description: '标准裤子后身部分工艺流程',
          crafts_count: 6,
          created_at: '2024-01-18T13:20:00Z',
          updated_at: '2024-01-21T09:10:00Z'
        },
        {
          id: 5,
          code: 'TPL005',
          name: '口袋制作工艺模板',
          part_type: 'POCKET',
          description: '各类服装口袋制作标准工艺',
          crafts_count: 2,
          created_at: '2024-01-19T15:45:00Z',
          updated_at: '2024-01-22T08:15:00Z'
        }
      ] : []

      // Simulate API response structure
      const response = {
        templates: sampleTemplates,
        total: sampleTemplates.length,
        page: 1,
        limit: 20,
        total_pages: 1
      }

      if (page === 1) {
        setTemplates(response.templates)
      } else {
        setTemplates(prev => [...prev, ...response.templates])
      }

      setPagination({
        page: response.page,
        limit: response.limit,
        total: response.total,
        totalPages: response.total_pages
      })
    } catch (error) {
      console.error('获取工艺模板列表失败:', error)
      Taro.showToast({
        title: '获取工艺模板列表失败',
        icon: 'none'
      })
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const applyFiltersAndSort = () => {
    let result = [...templates]

    // Apply search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase()
      result = result.filter(template =>
        template.code.toLowerCase().includes(searchTerm) ||
        template.name.toLowerCase().includes(searchTerm) ||
        template.part_type.toLowerCase().includes(searchTerm)
      )
    }

    // Apply part_type filter
    if (filters.part_type) {
      result = result.filter(template => template.part_type === filters.part_type)
    }

    // Apply sorting
    result.sort((a, b) => {
      let aValue: any = a[filters.sortBy as keyof CraftTemplate]
      let bValue: any = b[filters.sortBy as keyof CraftTemplate]

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      if (filters.sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    setFilteredTemplates(result)
  }

  const handleRefresh = () => {
    loadTemplates(1, true)
  }

  const handleLoadMore = () => {
    if (pagination.page < pagination.totalPages && !loading) {
      loadTemplates(pagination.page + 1)
    }
  }

  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }))
  }

  const handlePartTypeFilter = (partType: string) => {
    setFilters(prev => ({ ...prev, part_type: partType }))
  }

  const clearFilters = () => {
    setFilters({
      search: '',
      part_type: '',
      sortBy: 'created_at',
      sortOrder: 'desc'
    })
  }

  const navigateToDetail = (templateId: number) => {
    Taro.navigateTo({
      url: `/pages/craft-template-detail/index?templateId=${templateId}`
    })
  }

  const handleCreateTemplate = () => {
    Taro.showModal({
      title: '创建工艺模板',
      content: '此功能正在开发中，敬请期待！',
      showCancel: false,
      confirmText: '知道了'
    })
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  }

  const getPartTypeColor = (partType: string) => {
    const colors = {
      'FRONT_BODY': '#1890FF',
      'BACK_BODY': '#52C41A',
      'SLEEVE': '#722ED1',
      'COLLAR': '#FA8C16',
      'CUFF': '#F5222D',
      'POCKET': '#13C2C2'
    }
    return colors[partType as keyof typeof colors] || '#999999'
  }

  const renderTemplateCard = (template: CraftTemplate) => (
    <View
      key={template.id}
      className="template-card"
      onClick={() => navigateToDetail(template.id)}
    >
      <View className="template-card__header">
        <View className="template-card__title">
          <Text className="template-code">{template.code}</Text>
          <View
            className="part-type-badge"
            style={{ backgroundColor: getPartTypeColor(template.part_type) }}
          >
            <Text className="part-type-badge__text">{template.part_type}</Text>
          </View>
        </View>
        <Text className="template-card__date">{formatDate(template.created_at)}</Text>
      </View>

      <View className="template-card__content">
        <View className="template-card__row">
          <Text className="template-name">{template.name}</Text>
        </View>
        {template.description && (
          <View className="template-card__row">
            <Text className="template-description">{template.description}</Text>
          </View>
        )}
        <View className="template-card__row">
          <Text className="label">工艺数量:</Text>
          <Text className="value">{template.crafts_count}</Text>
        </View>
      </View>

      <View className="template-card__footer">
        <View className="template-card__meta">
          <Text className="template-id">ID: {template.id}</Text>
          <Text className="view-detail">查看详情 &gt;</Text>
        </View>
      </View>
    </View>
  )

  // Get unique part types for filter
  const partTypes = Array.from(new Set(templates.map(t => t.part_type)))

  return (
    <View className="craft-templates-container">
      {/* Search and Filter Header */}
      <View className="templates-header">
        <View className="search-section">
          <View className="search-input-wrapper">
            <Input
              className="search-input"
              placeholder="搜索模板代码、名称或部位类型"
              value={filters.search}
              onInput={(e) => handleSearch(e.detail.value)}
            />
          </View>
          <Button
            className="filter-btn"
            size="mini"
            onClick={() => setShowFilters(!showFilters)}
          >
            筛选
          </Button>
          <Button
            className="demo-btn"
            size="mini"
            onClick={() => {
              setUseSampleData(!useSampleData)
              loadTemplates(1, true)
            }}
          >
            {useSampleData ? '清空' : '示例'}
          </Button>
        </View>

        {showFilters && (
          <View className="filters-section">
            <View className="filter-group">
              <Text className="filter-label">部位类型:</Text>
              <View className="part-type-filters">
                <View
                  className={`part-type-filter ${filters.part_type === '' ? 'active' : ''}`}
                  onClick={() => handlePartTypeFilter('')}
                >
                  <Text className="part-type-filter__text">全部</Text>
                </View>
                {partTypes.map(partType => (
                  <View
                    key={partType}
                    className={`part-type-filter ${filters.part_type === partType ? 'active' : ''}`}
                    onClick={() => handlePartTypeFilter(partType)}
                  >
                    <Text className="part-type-filter__text">{partType}</Text>
                  </View>
                ))}
              </View>
            </View>

            <View className="filter-actions">
              <Button size="mini" onClick={clearFilters}>清除筛选</Button>
              <Button size="mini" onClick={() => setShowFilters(false)}>收起</Button>
            </View>
          </View>
        )}
      </View>

      {/* Templates List */}
      <ScrollView
        className="templates-list"
        scrollY
        refresherEnabled
        refresherTriggered={refreshing}
        onRefresherRefresh={handleRefresh}
        onScrollToLower={handleLoadMore}
      >
        {loading && filteredTemplates.length === 0 ? (
          <View className="loading-container">
            <Text>加载中...</Text>
          </View>
        ) : filteredTemplates.length === 0 ? (
          <View className="empty-container">
            <View className="empty-content">
              <Text className="empty-icon">📋</Text>
              <Text className="empty-title">暂无工艺模板</Text>
              <Text className="empty-description">
                {filters.search || filters.part_type
                  ? '没有找到符合条件的工艺模板，请尝试调整筛选条件'
                  : '还没有创建任何工艺模板'}
              </Text>
              {!filters.search && !filters.part_type && (
                <Button
                  className="create-btn"
                  type="primary"
                  onClick={handleCreateTemplate}
                >
                  创建工艺模板
                </Button>
              )}
            </View>
          </View>
        ) : (
          <>
            {filteredTemplates.map(renderTemplateCard)}
            {pagination.page < pagination.totalPages && (
              <View className="load-more">
                <Text>加载更多...</Text>
              </View>
            )}
          </>
        )}
      </ScrollView>
    </View>
  )
}

export default CraftTemplates
