.craft-template-detail-container {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow-x: hidden;
}

.template-header {
  background: #fff;
  padding: 20px 16px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 12px;

  &__title {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;

    .template-code {
      font-size: 20px;
      font-weight: bold;
      color: #333;
      flex: 1;
      min-width: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .template-name {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
    display: block;
  }

  .template-description {
    font-size: 14px;
    color: #666;
    margin-bottom: 16px;
    display: block;
    line-height: 1.5;
  }

  .template-meta {
    &__row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-size: 13px;
        color: #666;
        flex-shrink: 0;
        min-width: 80px;
      }

      .value {
        font-size: 13px;
        color: #333;
        text-align: right;
        flex: 1;
        margin-left: 8px;
        word-break: break-all;
      }
    }
  }
}

.part-type-badge {
  padding: 6px 12px;
  border-radius: 16px;
  flex-shrink: 0;

  &__text {
    font-size: 12px;
    color: #fff;
    font-weight: 500;
  }
}

.crafts-list {
  flex: 1;
  padding: 0 16px 16px;
  width: 100%;
  box-sizing: border-box;

  &__header {
    background: #fff;
    padding: 16px;
    border-radius: 12px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &__title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 4px;
    display: block;
  }

  &__subtitle {
    font-size: 12px;
    color: #999;
    display: block;
  }
}

.crafts-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.craft-item {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.98);
  }
}

.craft-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;

  .craft-title {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;

    .craft-code {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      flex-shrink: 0;
    }

    .craft-name {
      font-size: 14px;
      color: #666;
      flex: 1;
      min-width: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .craft-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .routes-count {
      font-size: 12px;
      color: #999;
    }

    .expand-icon {
      font-size: 12px;
      color: #667eea;
      transition: transform 0.2s ease;

      &.expanded {
        transform: rotate(0deg);
      }
    }
  }
}

.sequence-badge {
  background: #f0f0f0;
  padding: 4px 8px;
  border-radius: 12px;
  flex-shrink: 0;

  &__text {
    font-size: 10px;
    color: #666;
    font-weight: 500;
  }
}

.craft-description {
  padding: 0 16px 16px;
  border-bottom: 1px solid #f0f0f0;

  .description-text {
    font-size: 13px;
    color: #666;
    line-height: 1.5;
  }
}

.craft-routes {
  padding: 16px;

  &__header {
    margin-bottom: 12px;
  }

  &__title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
  }
}

.craft-route {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  border-left: 4px solid #667eea;

  &:last-child {
    margin-bottom: 0;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;

    .craft-route__title {
      flex: 1;
      min-width: 0;

      .route-code {
        font-size: 14px;
        font-weight: bold;
        color: #333;
        display: block;
        margin-bottom: 2px;
      }

      .route-name {
        font-size: 12px;
        color: #666;
        display: block;
      }
    }

    .route-price {
      font-size: 14px;
      font-weight: bold;
      color: #667eea;
      flex-shrink: 0;
    }
  }

  &__content {
    .craft-route__row {
      display: flex;
      align-items: flex-start;
      margin-bottom: 6px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-size: 11px;
        color: #666;
        flex-shrink: 0;
        min-width: 60px;
        margin-right: 8px;
      }

      .value {
        font-size: 11px;
        color: #333;
        flex: 1;
        word-break: break-all;

        &.notes {
          line-height: 1.4;
        }
      }
    }
  }
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  flex: 1;
}

.tag {
  padding: 2px 6px;
  border-radius: 8px;
  flex-shrink: 0;

  &--measurement {
    background: #e6f7ff;
    border: 1px solid #91d5ff;
  }

  &--registration {
    background: #f6ffed;
    border: 1px solid #b7eb8f;
  }

  &__text {
    font-size: 10px;
    color: #333;
  }
}

.loading-container,
.empty-container,
.empty-crafts {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #999;
  font-size: 14px;
}

// Responsive Design
@media (max-width: 480px) {
  .template-header {
    padding: 16px 12px;

    &__title {
      .template-code {
        font-size: 18px;
      }
    }

    .template-name {
      font-size: 15px;
    }

    .template-description {
      font-size: 13px;
    }

    .template-meta {
      &__row {
        .label,
        .value {
          font-size: 12px;
        }
      }
    }
  }

  .crafts-list {
    padding: 0 12px 12px;

    &__header {
      padding: 12px;
    }

    &__title {
      font-size: 15px;
    }

    &__subtitle {
      font-size: 11px;
    }
  }

  .craft-header {
    padding: 12px;

    .craft-title {
      .craft-code {
        font-size: 15px;
      }

      .craft-name {
        font-size: 13px;
      }
    }

    .craft-meta {
      .routes-count {
        font-size: 11px;
      }
    }
  }

  .craft-route {
    padding: 10px;

    &__header {
      .craft-route__title {
        .route-code {
          font-size: 13px;
        }

        .route-name {
          font-size: 11px;
        }
      }

      .route-price {
        font-size: 13px;
      }
    }

    &__content {
      .craft-route__row {
        .label,
        .value {
          font-size: 10px;
        }
      }
    }
  }
}

@media (min-width: 768px) {
  .craft-template-detail-container {
    max-width: 1200px;
    margin: 0 auto;
  }

  .crafts-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
    gap: 20px;
  }
}
