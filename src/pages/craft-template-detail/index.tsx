/* eslint-disable jsx-quotes */
import { useState, useEffect } from 'react'
import { View, Text, ScrollView } from '@tarojs/components'
import Taro, { useRouter } from '@tarojs/taro'
import ApiService from '../../services/api'
import StatusBadge from '../../components/StatusBadge'
import './index.scss'

interface CraftRoute {
  id: number
  code: string
  name: string
  skill_code: string
  skill_name: string
  measurement_types: string[]
  registration_types: string[]
  notes?: string
  price: number
  order: number
}

interface Craft {
  id: number
  code: string
  name: string
  sequence: number
  description?: string
  craft_routes: CraftRoute[]
}

interface CraftTemplateDetail {
  id: number
  code: string
  name: string
  part_type: string
  description?: string
  created_at: string
  updated_at: string
  crafts: Craft[]
}

const CraftTemplateDetail = () => {
  const router = useRouter()
  const { templateId } = router.params
  
  const [template, setTemplate] = useState<CraftTemplateDetail | null>(null)
  const [loading, setLoading] = useState(false)
  const [expandedCrafts, setExpandedCrafts] = useState<Set<number>>(new Set())

  useEffect(() => {
    if (templateId) {
      loadTemplateDetail(parseInt(templateId))
    }
  }, [templateId])

  const loadTemplateDetail = async (id: number) => {
    setLoading(true)
    try {
      const response = await ApiService.getCraftTemplateDetail(id)
      setTemplate(response)
    } catch (error) {
      console.error('获取工艺模板详情失败:', error)
      Taro.showToast({
        title: '获取工艺模板详情失败',
        icon: 'none'
      })
    } finally {
      setLoading(false)
    }
  }

  const toggleCraftExpansion = (craftId: number) => {
    const newExpanded = new Set(expandedCrafts)
    if (newExpanded.has(craftId)) {
      newExpanded.delete(craftId)
    } else {
      newExpanded.add(craftId)
    }
    setExpandedCrafts(newExpanded)
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatCurrency = (amount: number) => {
    return `¥${amount.toFixed(2)}`
  }

  const getPartTypeColor = (partType: string) => {
    const colors = {
      'FRONT_BODY': '#1890FF',
      'BACK_BODY': '#52C41A',
      'SLEEVE': '#722ED1',
      'COLLAR': '#FA8C16',
      'CUFF': '#F5222D',
      'POCKET': '#13C2C2'
    }
    return colors[partType as keyof typeof colors] || '#999999'
  }

  const renderCraftRoute = (route: CraftRoute) => (
    <View key={route.id} className="craft-route">
      <View className="craft-route__header">
        <View className="craft-route__title">
          <Text className="route-code">{route.code}</Text>
          <Text className="route-name">{route.name}</Text>
        </View>
        <Text className="route-price">{formatCurrency(route.price)}</Text>
      </View>

      <View className="craft-route__content">
        <View className="craft-route__row">
          <Text className="label">技能代码:</Text>
          <Text className="value">{route.skill_code}</Text>
        </View>
        <View className="craft-route__row">
          <Text className="label">技能名称:</Text>
          <Text className="value">{route.skill_name}</Text>
        </View>
        <View className="craft-route__row">
          <Text className="label">执行顺序:</Text>
          <Text className="value">{route.order}</Text>
        </View>
        
        {route.measurement_types && route.measurement_types.length > 0 && (
          <View className="craft-route__row">
            <Text className="label">测量类型:</Text>
            <View className="tags-container">
              {route.measurement_types.map((type, index) => (
                <View key={index} className="tag tag--measurement">
                  <Text className="tag__text">{type}</Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {route.registration_types && route.registration_types.length > 0 && (
          <View className="craft-route__row">
            <Text className="label">登记类型:</Text>
            <View className="tags-container">
              {route.registration_types.map((type, index) => (
                <View key={index} className="tag tag--registration">
                  <Text className="tag__text">{type}</Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {route.notes && (
          <View className="craft-route__row">
            <Text className="label">备注:</Text>
            <Text className="value notes">{route.notes}</Text>
          </View>
        )}
      </View>
    </View>
  )

  const renderCraft = (craft: Craft) => {
    const isExpanded = expandedCrafts.has(craft.id)
    
    return (
      <View key={craft.id} className="craft-item">
        <View 
          className="craft-header"
          onClick={() => toggleCraftExpansion(craft.id)}
        >
          <View className="craft-title">
            <Text className="craft-code">{craft.code}</Text>
            <Text className="craft-name">{craft.name}</Text>
            <View className="sequence-badge">
              <Text className="sequence-badge__text">序号: {craft.sequence}</Text>
            </View>
          </View>
          <View className="craft-meta">
            <Text className="routes-count">{craft.craft_routes.length} 个工艺路线</Text>
            <Text className={`expand-icon ${isExpanded ? 'expanded' : ''}`}>
              {isExpanded ? '▼' : '▶'}
            </Text>
          </View>
        </View>

        {craft.description && (
          <View className="craft-description">
            <Text className="description-text">{craft.description}</Text>
          </View>
        )}

        {isExpanded && (
          <View className="craft-routes">
            <View className="craft-routes__header">
              <Text className="craft-routes__title">工艺路线</Text>
            </View>
            {craft.craft_routes
              .sort((a, b) => a.order - b.order)
              .map(renderCraftRoute)}
          </View>
        )}
      </View>
    )
  }

  if (loading) {
    return (
      <View className="craft-template-detail-container">
        <View className="loading-container">
          <Text>加载中...</Text>
        </View>
      </View>
    )
  }

  if (!template) {
    return (
      <View className="craft-template-detail-container">
        <View className="empty-container">
          <Text>工艺模板不存在</Text>
        </View>
      </View>
    )
  }

  return (
    <View className="craft-template-detail-container">
      {/* Template Header */}
      <View className="template-header">
        <View className="template-header__title">
          <Text className="template-code">{template.code}</Text>
          <View 
            className="part-type-badge"
            style={{ backgroundColor: getPartTypeColor(template.part_type) }}
          >
            <Text className="part-type-badge__text">{template.part_type}</Text>
          </View>
        </View>
        
        <Text className="template-name">{template.name}</Text>
        
        {template.description && (
          <Text className="template-description">{template.description}</Text>
        )}

        <View className="template-meta">
          <View className="template-meta__row">
            <Text className="label">模板ID:</Text>
            <Text className="value">{template.id}</Text>
          </View>
          <View className="template-meta__row">
            <Text className="label">工艺数量:</Text>
            <Text className="value">{template.crafts.length}</Text>
          </View>
          <View className="template-meta__row">
            <Text className="label">创建时间:</Text>
            <Text className="value">{formatDate(template.created_at)}</Text>
          </View>
          <View className="template-meta__row">
            <Text className="label">更新时间:</Text>
            <Text className="value">{formatDate(template.updated_at)}</Text>
          </View>
        </View>
      </View>

      {/* Crafts List */}
      <ScrollView className="crafts-list" scrollY>
        <View className="crafts-list__header">
          <Text className="crafts-list__title">工艺列表</Text>
          <Text className="crafts-list__subtitle">
            共 {template.crafts.length} 个工艺，点击展开查看工艺路线
          </Text>
        </View>

        {template.crafts.length === 0 ? (
          <View className="empty-crafts">
            <Text>该模板暂无工艺</Text>
          </View>
        ) : (
          <View className="crafts-container">
            {template.crafts
              .sort((a, b) => a.sequence - b.sequence)
              .map(renderCraft)}
          </View>
        )}
      </ScrollView>
    </View>
  )
}

export default CraftTemplateDetail
