import { useState, useEffect } from 'react'
import { View, Text, Button, ScrollView, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import './index.scss'

interface UserInfo {
  id: string
  username: string
  name: string
  email: string
  role: 'manager' | 'worker'
  avatar_url?: string
  factory_name: string
  department_name: string
  joined_at: string
}

const Profile = () => {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    loadUserInfo()
  }, [])

  const loadUserInfo = async () => {
    setLoading(true)
    try {
      // Get user info from storage first
      const savedUserInfo = Taro.getStorageSync('userInfo')
      if (savedUserInfo) {
        setUserInfo(savedUserInfo)
      }
      
      // TODO: Replace with actual API call to get fresh user data
      // const response = await ApiService.getCurrentUser()
      // setUserInfo(response)
    } catch (error) {
      console.error('获取用户信息失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleLogout = () => {
    Taro.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          Taro.removeStorageSync('token')
          Taro.removeStorageSync('userInfo')
          Taro.redirectTo({
            url: '/pages/login/index'
          })
        }
      }
    })
  }

  const navigateTo = (page: string) => {
    Taro.navigateTo({
      url: `/pages/${page}/index`
    })
  }

  const menuItems = [
    { key: 'employee-management', title: '员工管理', icon: '👥', page: 'employee-management' },
    { key: 'department-management', title: '部门管理', icon: '🏢', page: 'department-management' },
    { key: 'settings', title: '设置', icon: '⚙️', page: 'settings' },
    { key: 'help', title: '帮助与反馈', icon: '❓', page: 'help' },
    { key: 'about', title: '关于我们', icon: 'ℹ️', page: 'about' }
  ]

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
  }

  if (loading) {
    return (
      <View className="loading-container">
        <Text>加载中...</Text>
      </View>
    )
  }

  return (
    <View className="profile-container">
      <ScrollView className="profile-content" scrollY>
        {/* User Info Section */}
        <View className="user-section">
          <View className="user-avatar">
            {userInfo?.avatar_url ? (
              <Image className="avatar-image" src={userInfo.avatar_url} />
            ) : (
              <View className="avatar-placeholder">
                <Text className="avatar-text">{userInfo?.name?.charAt(0) || 'U'}</Text>
              </View>
            )}
          </View>
          
          <View className="user-info">
            <Text className="user-name">{userInfo?.name || '未知用户'}</Text>
            <Text className="user-role">
              {userInfo?.role === 'manager' ? '管理员' : '工人'}
            </Text>
            <Text className="user-email">{userInfo?.email || ''}</Text>
          </View>
        </View>

        {/* User Details Section */}
        <View className="details-section">
          <View className="section-header">
            <Text className="section-title">基本信息</Text>
          </View>
          
          <View className="details-card">
            <View className="detail-row">
              <Text className="detail-label">用户名</Text>
              <Text className="detail-value">{userInfo?.username || '-'}</Text>
            </View>
            <View className="detail-row">
              <Text className="detail-label">工厂</Text>
              <Text className="detail-value">{userInfo?.factory_name || '-'}</Text>
            </View>
            <View className="detail-row">
              <Text className="detail-label">部门</Text>
              <Text className="detail-value">{userInfo?.department_name || '-'}</Text>
            </View>
            <View className="detail-row">
              <Text className="detail-label">加入时间</Text>
              <Text className="detail-value">
                {userInfo?.joined_at ? formatDate(userInfo.joined_at) : '-'}
              </Text>
            </View>
          </View>
        </View>

        {/* Menu Section */}
        <View className="menu-section">
          <View className="section-header">
            <Text className="section-title">功能菜单</Text>
          </View>
          
          <View className="menu-list">
            {menuItems.map((item) => (
              <View 
                key={item.key}
                className="menu-item"
                onClick={() => navigateTo(item.page)}
              >
                <View className="menu-icon">
                  <Text className="icon-text">{item.icon}</Text>
                </View>
                <Text className="menu-title">{item.title}</Text>
                <View className="menu-arrow">
                  <Text className="arrow-text">›</Text>
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Logout Section */}
        <View className="logout-section">
          <Button className="logout-btn" onClick={handleLogout}>
            退出登录
          </Button>
        </View>
      </ScrollView>
    </View>
  )
}

export default Profile
