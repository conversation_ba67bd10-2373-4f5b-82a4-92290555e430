.profile-container {
  min-height: 100vh;
  background: #f5f5f5;
}

.profile-content {
  height: 100vh;
  padding-bottom: 100px; // Account for tab bar
}

.user-section {
  background: linear-gradient(135deg, #667eea, #764ba2);
  padding: 40px 20px 30px;
  display: flex;
  align-items: center;
  color: #fff;
  
  .user-avatar {
    margin-right: 20px;
    
    .avatar-image {
      width: 80px;
      height: 80px;
      border-radius: 40px;
      border: 3px solid rgba(255, 255, 255, 0.3);
    }
    
    .avatar-placeholder {
      width: 80px;
      height: 80px;
      border-radius: 40px;
      background: rgba(255, 255, 255, 0.2);
      border: 3px solid rgba(255, 255, 255, 0.3);
      display: flex;
      align-items: center;
      justify-content: center;
      
      .avatar-text {
        font-size: 32px;
        font-weight: bold;
        color: #fff;
      }
    }
  }
  
  .user-info {
    flex: 1;
    
    .user-name {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 6px;
      display: block;
    }
    
    .user-role {
      font-size: 14px;
      background: rgba(255, 255, 255, 0.2);
      padding: 4px 12px;
      border-radius: 12px;
      display: inline-block;
      margin-bottom: 8px;
    }
    
    .user-email {
      font-size: 12px;
      opacity: 0.8;
      display: block;
    }
  }
}

.details-section,
.menu-section {
  margin: 20px;
  
  .section-header {
    margin-bottom: 12px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
    }
  }
}

.details-card {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .detail-label {
      font-size: 14px;
      color: #666;
    }
    
    .detail-value {
      font-size: 14px;
      color: #333;
      font-weight: 500;
    }
  }
}

.menu-list {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  
  .menu-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
    
    &:last-child {
      border-bottom: none;
    }
    
    &:active {
      background-color: #f8f9fa;
    }
    
    .menu-icon {
      width: 40px;
      height: 40px;
      border-radius: 20px;
      background: #f8f9ff;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      
      .icon-text {
        font-size: 20px;
      }
    }
    
    .menu-title {
      flex: 1;
      font-size: 14px;
      color: #333;
    }
    
    .menu-arrow {
      .arrow-text {
        font-size: 18px;
        color: #ccc;
      }
    }
  }
}

.logout-section {
  margin: 20px;
  
  .logout-btn {
    width: 100%;
    background: #ff4757;
    color: #fff;
    border: none;
    border-radius: 12px;
    padding: 16px;
    font-size: 16px;
    font-weight: bold;
    
    &:active {
      opacity: 0.8;
    }
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  color: #999;
  font-size: 14px;
}

// Responsive Design
@media (max-width: 480px) {
  .user-section {
    padding: 30px 16px 20px;
    
    .user-avatar {
      margin-right: 16px;
      
      .avatar-image,
      .avatar-placeholder {
        width: 60px;
        height: 60px;
        border-radius: 30px;
      }
      
      .avatar-placeholder {
        .avatar-text {
          font-size: 24px;
        }
      }
    }
    
    .user-info {
      .user-name {
        font-size: 20px;
      }
      
      .user-role {
        font-size: 12px;
      }
      
      .user-email {
        font-size: 11px;
      }
    }
  }
  
  .details-section,
  .menu-section,
  .logout-section {
    margin: 16px;
  }
  
  .details-card,
  .menu-item {
    padding: 12px;
  }
}
