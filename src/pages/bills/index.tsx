import { useState, useEffect } from 'react'
import { View, Text, ScrollView, Button } from '@tarojs/components'
import Taro from '@tarojs/taro'
import './index.scss'

interface Bill {
  id: number
  bill_no: string
  order_no: string
  bill_type: 'INCOME' | 'EXPENSE'
  amount: number
  status: 'PENDING' | 'PAID' | 'OVERDUE' | 'CANCELLED'
  due_date: string
  created_at: string
  description: string
}

const Bills = () => {
  const [bills, setBills] = useState<Bill[]>([])
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState<'all' | 'pending' | 'paid'>('all')

  useEffect(() => {
    loadBills()
  }, [])

  const loadBills = async () => {
    setLoading(true)
    try {
      // TODO: Replace with actual API call
      // const response = await ApiService.getBillList()
      // setBills(response.bills)
      
      // Mock data for now
      const mockBills: Bill[] = [
        {
          id: 1,
          bill_no: 'BILL001',
          order_no: 'ORD001',
          bill_type: 'INCOME',
          amount: 15000,
          status: 'PENDING',
          due_date: '2025-07-15',
          created_at: '2025-06-26',
          description: '订单收款'
        },
        {
          id: 2,
          bill_no: 'BILL002',
          order_no: 'ORD002',
          bill_type: 'EXPENSE',
          amount: 8500,
          status: 'PAID',
          due_date: '2025-06-30',
          created_at: '2025-06-25',
          description: '材料采购费用'
        }
      ]
      setBills(mockBills)
    } catch (error) {
      console.error('获取账单列表失败:', error)
      Taro.showToast({
        title: '获取账单列表失败',
        icon: 'none'
      })
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    const colors = {
      'PENDING': '#FFA500',
      'PAID': '#52C41A',
      'OVERDUE': '#FF4D4F',
      'CANCELLED': '#999999'
    }
    return colors[status] || '#999999'
  }

  const getStatusText = (status: string) => {
    const texts = {
      'PENDING': '待付款',
      'PAID': '已付款',
      'OVERDUE': '已逾期',
      'CANCELLED': '已取消'
    }
    return texts[status] || status
  }

  const getBillTypeText = (type: string) => {
    return type === 'INCOME' ? '收入' : '支出'
  }

  const formatCurrency = (amount: number) => {
    return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
  }

  const filteredBills = bills.filter(bill => {
    if (activeTab === 'all') return true
    if (activeTab === 'pending') return bill.status === 'PENDING'
    if (activeTab === 'paid') return bill.status === 'PAID'
    return true
  })

  const renderBillCard = (bill: Bill) => (
    <View key={bill.id} className="bill-card">
      <View className="bill-header">
        <View className="bill-info">
          <Text className="bill-no">{bill.bill_no}</Text>
          <Text className="order-no">订单: {bill.order_no}</Text>
        </View>
        <View className="bill-status">
          <View 
            className="status-badge"
            style={{ backgroundColor: getStatusColor(bill.status) }}
          >
            <Text className="status-text">{getStatusText(bill.status)}</Text>
          </View>
        </View>
      </View>

      <View className="bill-content">
        <View className="bill-row">
          <Text className="label">类型:</Text>
          <Text className="value">{getBillTypeText(bill.bill_type)}</Text>
        </View>
        <View className="bill-row">
          <Text className="label">金额:</Text>
          <Text 
            className={`amount ${bill.bill_type === 'INCOME' ? 'income' : 'expense'}`}
          >
            {bill.bill_type === 'INCOME' ? '+' : '-'}{formatCurrency(bill.amount)}
          </Text>
        </View>
        <View className="bill-row">
          <Text className="label">到期日期:</Text>
          <Text className="value">{formatDate(bill.due_date)}</Text>
        </View>
        <View className="bill-row">
          <Text className="label">描述:</Text>
          <Text className="value">{bill.description}</Text>
        </View>
      </View>

      <View className="bill-footer">
        <Text className="created-date">创建于 {formatDate(bill.created_at)}</Text>
        {bill.status === 'PENDING' && (
          <Button className="pay-btn" size="mini">
            立即付款
          </Button>
        )}
      </View>
    </View>
  )

  return (
    <View className="bills-container">
      {/* Tab Header */}
      <View className="tabs-header">
        <View className="tabs">
          <View 
            className={`tab ${activeTab === 'all' ? 'active' : ''}`}
            onClick={() => setActiveTab('all')}
          >
            <Text className="tab-text">全部</Text>
          </View>
          <View 
            className={`tab ${activeTab === 'pending' ? 'active' : ''}`}
            onClick={() => setActiveTab('pending')}
          >
            <Text className="tab-text">待付款</Text>
          </View>
          <View 
            className={`tab ${activeTab === 'paid' ? 'active' : ''}`}
            onClick={() => setActiveTab('paid')}
          >
            <Text className="tab-text">已付款</Text>
          </View>
        </View>
      </View>

      {/* Bills List */}
      <ScrollView className="bills-list" scrollY>
        {loading ? (
          <View className="loading-container">
            <Text>加载中...</Text>
          </View>
        ) : filteredBills.length === 0 ? (
          <View className="empty-container">
            <Text>暂无账单数据</Text>
          </View>
        ) : (
          filteredBills.map(renderBillCard)
        )}
      </ScrollView>
    </View>
  )
}

export default Bills
