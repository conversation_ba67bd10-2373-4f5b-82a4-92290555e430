.bills-container {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.tabs-header {
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.tabs {
  display: flex;
  
  .tab {
    flex: 1;
    padding: 16px;
    text-align: center;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
    
    &.active {
      border-bottom-color: #667eea;
      
      .tab-text {
        color: #667eea;
        font-weight: bold;
      }
    }
    
    &-text {
      font-size: 14px;
      color: #666;
    }
  }
}

.bills-list {
  flex: 1;
  padding: 16px;
}

.bill-card {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .bill-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
    
    .bill-info {
      flex: 1;
      
      .bill-no {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        display: block;
        margin-bottom: 4px;
      }
      
      .order-no {
        font-size: 12px;
        color: #666;
        display: block;
      }
    }
    
    .bill-status {
      .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        
        .status-text {
          color: #fff;
          font-size: 12px;
          font-weight: 500;
        }
      }
    }
  }
  
  .bill-content {
    margin-bottom: 12px;
    
    .bill-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 6px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .label {
        font-size: 12px;
        color: #666;
        flex-shrink: 0;
        min-width: 80px;
      }
      
      .value {
        font-size: 12px;
        color: #333;
        text-align: right;
        flex: 1;
        margin-left: 8px;
        word-break: break-all;
      }
      
      .amount {
        font-size: 14px;
        font-weight: bold;
        
        &.income {
          color: #52C41A;
        }
        
        &.expense {
          color: #FF4D4F;
        }
      }
    }
  }
  
  .bill-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #f0f0f0;
    padding-top: 12px;
    
    .created-date {
      font-size: 11px;
      color: #999;
    }
    
    .pay-btn {
      background: #667eea;
      color: #fff;
      border: none;
      border-radius: 16px;
      padding: 6px 12px;
      font-size: 12px;
      
      &:active {
        opacity: 0.8;
      }
    }
  }
}

.loading-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #999;
  font-size: 14px;
}

// Responsive Design
@media (max-width: 480px) {
  .bills-list {
    padding: 12px;
  }
  
  .bill-card {
    padding: 12px;
    
    .bill-header {
      .bill-info {
        .bill-no {
          font-size: 14px;
        }
      }
    }
    
    .bill-content {
      .bill-row {
        .label,
        .value {
          font-size: 11px;
        }
        
        .amount {
          font-size: 13px;
        }
      }
    }
  }
}
