from typing import List, Optional, Dict, Any, Callable
from datetime import datetime
from sqlalchemy import and_, or_, func, select, update, delete
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.ext.asyncio import AsyncSession
from src.application.interfaces.order_craft_instance_repository_interface import OrderCraftInstanceRepositoryInterface
from src.domain.entities.order_craft_instance import OrderCraftInstance, OrderCraftInstanceStatus


class OrderCraftInstanceRepository(OrderCraftInstanceRepositoryInterface):
    """Repository implementation for OrderCraftInstance operations."""

    def __init__(self, session_factory: Callable[[], AsyncSession]):
        self.session_factory = session_factory

    async def create(self, order_craft_instance: OrderCraftInstance) -> OrderCraftInstance:
        """Create a new order craft instance."""
        async with self.session_factory() as session:
            session.add(order_craft_instance)
            await session.commit()
            await session.refresh(order_craft_instance)
            return order_craft_instance

    async def get_by_id(self, instance_id: int) -> Optional[OrderCraftInstance]:
        """Get order craft instance by ID with related entities."""
        async with self.session_factory() as session:
            stmt = (
                select(OrderCraftInstance)
                .where(OrderCraftInstance.id == instance_id)
                # Remove relationship loading to avoid circular import issues
                # .options(
                #     joinedload(OrderCraftInstance.order),
                #     joinedload(OrderCraftInstance.order_part),
                #     joinedload(OrderCraftInstance.order_craft),
                #     joinedload(OrderCraftInstance.assigned_user),
                #     joinedload(OrderCraftInstance.supervisor),
                #     selectinload(OrderCraftInstance.order_craft_route_instances)
                # )
            )
            result = await session.execute(stmt)
            return result.scalar_one_or_none()

    async def get_by_order_and_part_and_craft(
        self, 
        order_no: str, 
        order_part_no: str, 
        craft_code: str, 
        factory_id: int
    ) -> Optional[OrderCraftInstance]:
        """Get order craft instance by order, part, and craft."""
        async with self.session_factory() as session:
            stmt = (
                select(OrderCraftInstance)
                .where(
                    and_(
                        OrderCraftInstance.order_no == order_no,
                        OrderCraftInstance.order_part_no == order_part_no,
                        OrderCraftInstance.craft_code == craft_code,
                        OrderCraftInstance.factory_id == factory_id
                    )
                )
                # Remove relationship loading to avoid circular import issues
                # .options(
                #     joinedload(OrderCraftInstance.order),
                #     joinedload(OrderCraftInstance.order_part),
                #     joinedload(OrderCraftInstance.order_craft)
                # )
            )
            result = await session.execute(stmt)
            return result.scalar_one_or_none()

    async def get_by_order_no(self, order_no: str, factory_id: int) -> List[OrderCraftInstance]:
        """Get all order craft instances for a specific order."""
        async with self.session_factory() as session:
            stmt = (
                select(OrderCraftInstance)
                .where(
                    and_(
                        OrderCraftInstance.order_no == order_no,
                        OrderCraftInstance.factory_id == factory_id
                    )
                )
                .order_by(OrderCraftInstance.order_part_no, OrderCraftInstance.sequence_order)
                # Remove relationship loading to avoid circular import issues
                # .options(
                #     joinedload(OrderCraftInstance.order_part),
                #     joinedload(OrderCraftInstance.order_craft)
                # )
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_by_order_part_no(self, order_part_no: str, factory_id: int) -> List[OrderCraftInstance]:
        """Get all order craft instances for a specific order part."""
        async with self.session_factory() as session:
            stmt = (
                select(OrderCraftInstance)
                .where(
                    and_(
                        OrderCraftInstance.order_part_no == order_part_no,
                        OrderCraftInstance.factory_id == factory_id
                    )
                )
                .order_by(OrderCraftInstance.sequence_order)
                # Remove relationship loading to avoid circular import issues
                # .options(
                #     joinedload(OrderCraftInstance.order_craft)
                # )
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_by_craft_code(self, craft_code: str, factory_id: int) -> List[OrderCraftInstance]:
        """Get all order craft instances for a specific craft."""
        async with self.session_factory() as session:
            stmt = (
                select(OrderCraftInstance)
                .where(
                    and_(
                        OrderCraftInstance.craft_code == craft_code,
                        OrderCraftInstance.factory_id == factory_id
                    )
                )
                .order_by(OrderCraftInstance.order_no, OrderCraftInstance.order_part_no)
                # Remove relationship loading to avoid circular import issues
                # .options(
                #     joinedload(OrderCraftInstance.order),
                #     joinedload(OrderCraftInstance.order_part)
                # )
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_by_status(
        self, 
        status: OrderCraftInstanceStatus, 
        factory_id: int, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[OrderCraftInstance]:
        """Get order craft instances by status."""
        async with self.session_factory() as session:
            stmt = (
                select(OrderCraftInstance)
                .where(
                    and_(
                        OrderCraftInstance.status == status,
                        OrderCraftInstance.factory_id == factory_id
                    )
                )
                .order_by(OrderCraftInstance.created_at.desc())
                .offset(skip)
                .limit(limit)
                # Remove relationship loading to avoid circular import issues
                # .options(
                #     joinedload(OrderCraftInstance.order),
                #     joinedload(OrderCraftInstance.order_part)
                # )
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_by_assigned_user(
        self, 
        user_id: int, 
        factory_id: int, 
        status: Optional[OrderCraftInstanceStatus] = None
    ) -> List[OrderCraftInstance]:
        """Get order craft instances assigned to a specific user."""
        async with self.session_factory() as session:
            conditions = [
                OrderCraftInstance.assigned_user_id == user_id,
                OrderCraftInstance.factory_id == factory_id
            ]
            
            if status:
                conditions.append(OrderCraftInstance.status == status)
            
            stmt = (
                select(OrderCraftInstance)
                .where(and_(*conditions))
                .order_by(OrderCraftInstance.planned_start_date.asc().nullslast())
                # Remove relationship loading to avoid circular import issues
                # .options(
                #     joinedload(OrderCraftInstance.order),
                #     joinedload(OrderCraftInstance.order_part)
                # )
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_by_supervisor(
        self, 
        supervisor_id: int, 
        factory_id: int, 
        status: Optional[OrderCraftInstanceStatus] = None
    ) -> List[OrderCraftInstance]:
        """Get order craft instances supervised by a specific user."""
        async with self.session_factory() as session:
            conditions = [
                OrderCraftInstance.supervisor_user_id == supervisor_id,
                OrderCraftInstance.factory_id == factory_id
            ]
            
            if status:
                conditions.append(OrderCraftInstance.status == status)
            
            stmt = (
                select(OrderCraftInstance)
                .where(and_(*conditions))
                .order_by(OrderCraftInstance.planned_start_date.asc().nullslast())
                # Remove relationship loading to avoid circular import issues
                # .options(
                #     joinedload(OrderCraftInstance.order),
                #     joinedload(OrderCraftInstance.order_part),
                #     joinedload(OrderCraftInstance.assigned_user)
                # )
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_by_part_type(
        self, 
        part_type: str, 
        factory_id: int, 
        status: Optional[OrderCraftInstanceStatus] = None
    ) -> List[OrderCraftInstance]:
        """Get order craft instances by part type."""
        async with self.session_factory() as session:
            conditions = [
                OrderCraftInstance.part_type == part_type,
                OrderCraftInstance.factory_id == factory_id
            ]
            
            if status:
                conditions.append(OrderCraftInstance.status == status)
            
            stmt = (
                select(OrderCraftInstance)
                .where(and_(*conditions))
                .order_by(OrderCraftInstance.order_no, OrderCraftInstance.order_part_no)
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_by_date_range(
        self,
        factory_id: int,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        date_field: str = "created_at"
    ) -> List[OrderCraftInstance]:
        """Get order craft instances within a date range."""
        async with self.session_factory() as session:
            conditions = [OrderCraftInstance.factory_id == factory_id]
            
            date_column = getattr(OrderCraftInstance, date_field)
            
            if start_date:
                conditions.append(date_column >= start_date)
            if end_date:
                conditions.append(date_column <= end_date)
            
            stmt = (
                select(OrderCraftInstance)
                .where(and_(*conditions))
                .order_by(date_column.desc())
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_overdue_instances(self, factory_id: int) -> List[OrderCraftInstance]:
        """Get all overdue order craft instances."""
        async with self.session_factory() as session:
            now = datetime.now()
            stmt = (
                select(OrderCraftInstance)
                .where(
                    and_(
                        OrderCraftInstance.factory_id == factory_id,
                        OrderCraftInstance.planned_end_date < now,
                        OrderCraftInstance.status.in_([
                            OrderCraftInstanceStatus.PENDING,
                            OrderCraftInstanceStatus.IN_PROGRESS,
                            OrderCraftInstanceStatus.ON_HOLD
                        ])
                    )
                )
                .order_by(OrderCraftInstance.planned_end_date.asc())
                # Remove relationship loading to avoid circular import issues
                # .options(
                #     joinedload(OrderCraftInstance.order),
                #     joinedload(OrderCraftInstance.order_part)
                # )
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_in_progress_instances(self, factory_id: int) -> List[OrderCraftInstance]:
        """Get all in-progress order craft instances."""
        return await self.get_by_status(OrderCraftInstanceStatus.IN_PROGRESS, factory_id)

    async def get_pending_instances(self, factory_id: int) -> List[OrderCraftInstance]:
        """Get all pending order craft instances."""
        return await self.get_by_status(OrderCraftInstanceStatus.PENDING, factory_id)

    async def search_instances(
        self,
        factory_id: int,
        search_params: Dict[str, Any],
        skip: int = 0,
        limit: int = 100
    ) -> List[OrderCraftInstance]:
        """Search order craft instances with flexible parameters."""
        async with self.session_factory() as session:
            conditions = [OrderCraftInstance.factory_id == factory_id]
            
            # Build search conditions
            if search_term := search_params.get("search_term"):
                conditions.append(
                    or_(
                        OrderCraftInstance.order_no.ilike(f"%{search_term}%"),
                        OrderCraftInstance.order_part_no.ilike(f"%{search_term}%"),
                        OrderCraftInstance.craft_code.ilike(f"%{search_term}%")
                    )
                )
            
            if status := search_params.get("status"):
                conditions.append(OrderCraftInstance.status == status)
            
            if part_type := search_params.get("part_type"):
                conditions.append(OrderCraftInstance.part_type == part_type)
            
            if assigned_user_id := search_params.get("assigned_user_id"):
                conditions.append(OrderCraftInstance.assigned_user_id == assigned_user_id)
            
            if supervisor_id := search_params.get("supervisor_id"):
                conditions.append(OrderCraftInstance.supervisor_user_id == supervisor_id)
            
            if start_date := search_params.get("start_date"):
                conditions.append(OrderCraftInstance.created_at >= start_date)
            
            if end_date := search_params.get("end_date"):
                conditions.append(OrderCraftInstance.created_at <= end_date)
            
            stmt = (
                select(OrderCraftInstance)
                .where(and_(*conditions))
                .order_by(OrderCraftInstance.created_at.desc())
                .offset(skip)
                .limit(limit)
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def update(self, order_craft_instance: OrderCraftInstance) -> OrderCraftInstance:
        """Update an existing order craft instance."""
        async with self.session_factory() as session:
            await session.merge(order_craft_instance)
            await session.commit()
            await session.refresh(order_craft_instance)
            return order_craft_instance

    async def delete(self, instance_id: int) -> bool:
        """Delete an order craft instance."""
        async with self.session_factory() as session:
            stmt = delete(OrderCraftInstance).where(OrderCraftInstance.id == instance_id)
            result = await session.execute(stmt)
            await session.commit()
            return result.rowcount > 0

    async def bulk_create(self, instances: List[OrderCraftInstance]) -> List[OrderCraftInstance]:
        """Create multiple order craft instances."""
        async with self.session_factory() as session:
            session.add_all(instances)
            await session.commit()
            for instance in instances:
                await session.refresh(instance)
            return instances

    async def bulk_update_status(
        self, 
        instance_ids: List[int], 
        status: OrderCraftInstanceStatus,
        notes: Optional[str] = None
    ) -> List[OrderCraftInstance]:
        """Bulk update status for multiple instances."""
        async with self.session_factory() as session:
            update_data = {
                "status": status,
                "updated_at": datetime.now()
            }
            
            if notes:
                update_data["notes"] = notes
            
            stmt = (
                update(OrderCraftInstance)
                .where(OrderCraftInstance.id.in_(instance_ids))
                .values(**update_data)
            )
            await session.execute(stmt)
            await session.commit()
            
            # Return updated instances
            return await self.get_by_ids(instance_ids)

    async def get_statistics_by_order(self, order_no: str, factory_id: int) -> Dict[str, Any]:
        """Get statistics for order craft instances by order."""
        async with self.session_factory() as session:
            stmt = (
                select(
                    func.count(OrderCraftInstance.id).label("total"),
                    func.sum(func.case(
                        (OrderCraftInstance.status == OrderCraftInstanceStatus.COMPLETED, 1),
                        else_=0
                    )).label("completed"),
                    func.sum(func.case(
                        (OrderCraftInstance.status == OrderCraftInstanceStatus.IN_PROGRESS, 1),
                        else_=0
                    )).label("in_progress"),
                    func.sum(func.case(
                        (OrderCraftInstance.status == OrderCraftInstanceStatus.PENDING, 1),
                        else_=0
                    )).label("pending"),
                    func.avg(OrderCraftInstance.progress_percentage).label("avg_progress"),
                    func.sum(OrderCraftInstance.rework_count).label("total_rework"),
                    func.sum(OrderCraftInstance.defect_count).label("total_defects")
                )
                .where(
                    and_(
                        OrderCraftInstance.order_no == order_no,
                        OrderCraftInstance.factory_id == factory_id
                    )
                )
            )
            result = await session.execute(stmt)
            row = result.first()
            
            return {
                "total": row.total or 0,
                "completed": row.completed or 0,
                "in_progress": row.in_progress or 0,
                "pending": row.pending or 0,
                "avg_progress": float(row.avg_progress or 0),
                "total_rework": row.total_rework or 0,
                "total_defects": row.total_defects or 0
            }

    async def get_statistics_by_part_type(self, factory_id: int) -> Dict[str, Any]:
        """Get statistics for order craft instances by part type."""
        # Implementation similar to above but grouping by part_type
        return {}

    async def get_statistics_by_craft(self, factory_id: int) -> Dict[str, Any]:
        """Get statistics for order craft instances by craft."""
        # Implementation similar to above but grouping by craft_code
        return {}

    async def get_statistics_by_user(self, user_id: int, factory_id: int) -> Dict[str, Any]:
        """Get statistics for order craft instances by user."""
        # Implementation similar to above but filtering by user_id
        return {}

    async def get_workflow_status(self, order_no: str, order_part_no: str, factory_id: int) -> List[OrderCraftInstance]:
        """Get workflow status for a specific order part (all crafts for the part)."""
        async with self.session_factory() as session:
            stmt = (
                select(OrderCraftInstance)
                .where(
                    and_(
                        OrderCraftInstance.order_no == order_no,
                        OrderCraftInstance.order_part_no == order_part_no,
                        OrderCraftInstance.factory_id == factory_id
                    )
                )
                .order_by(OrderCraftInstance.sequence_order)
                # Remove relationship loading to avoid circular import issues
                # .options(
                #     joinedload(OrderCraftInstance.order_craft)
                # )
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_next_craft_instances(self, order_no: str, order_part_no: str, factory_id: int) -> List[OrderCraftInstance]:
        """Get next available craft instances for a specific order part."""
        async with self.session_factory() as session:
            stmt = (
                select(OrderCraftInstance)
                .where(
                    and_(
                        OrderCraftInstance.order_no == order_no,
                        OrderCraftInstance.order_part_no == order_part_no,
                        OrderCraftInstance.factory_id == factory_id,
                        OrderCraftInstance.status == OrderCraftInstanceStatus.PENDING,
                        OrderCraftInstance.is_active == True
                    )
                )
                .order_by(OrderCraftInstance.sequence_order)
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def count_by_status(self, factory_id: int) -> Dict[str, int]:
        """Count order craft instances by status."""
        async with self.session_factory() as session:
            stmt = (
                select(
                    OrderCraftInstance.status,
                    func.count(OrderCraftInstance.id).label("count")
                )
                .where(OrderCraftInstance.factory_id == factory_id)
                .group_by(OrderCraftInstance.status)
            )
            result = await session.execute(stmt)
            
            counts = {status.value: 0 for status in OrderCraftInstanceStatus}
            for row in result:
                counts[row.status.value] = row.count
            
            return counts

    async def get_performance_metrics(
        self, 
        factory_id: int, 
        start_date: Optional[datetime] = None, 
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get performance metrics for order craft instances."""
        # Basic implementation - can be expanded
        return {
            "total": 0,
            "completed": 0,
            "completion_rate": 0,
            "avg_progress": 0,
            "avg_duration_minutes": 0,
            "avg_quality_score": 0,
            "total_rework": 0,
            "total_defects": 0,
            "overdue_count": 0,
            "on_time_rate": 0
        }

    async def get_by_ids(self, instance_ids: List[int]) -> List[OrderCraftInstance]:
        """Get multiple order craft instances by IDs."""
        async with self.session_factory() as session:
            stmt = (
                select(OrderCraftInstance)
                .where(OrderCraftInstance.id.in_(instance_ids))
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())