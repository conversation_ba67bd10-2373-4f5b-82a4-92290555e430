from typing import List, Optional, Dict, Any, Callable
from datetime import datetime, timedelta
from sqlalchemy import and_, or_, func, select, update, delete, text
from sqlalchemy.orm import selectinload
from sqlalchemy.ext.asyncio import AsyncSession
from src.application.interfaces.create_template_repository_interface import CreateTemplateRepositoryInterface
from src.domain.entities.create_template import CreateTemplate, TemplateStatus


class CreateTemplateRepository(CreateTemplateRepositoryInterface):
    """Repository implementation for CreateTemplate operations."""

    def __init__(self, session_factory: Callable[[], AsyncSession]):
        self.session_factory = session_factory

    async def create(self, template: CreateTemplate) -> CreateTemplate:
        """Create a new template."""
        async with self.session_factory() as session:
            session.add(template)
            await session.commit()
            await session.refresh(template)
            return template

    async def get_by_id(self, template_id: int) -> Optional[CreateTemplate]:
        """Get template by ID."""
        async with self.session_factory() as session:
            stmt = select(CreateTemplate).where(CreateTemplate.id == template_id)
            result = await session.execute(stmt)
            return result.scalar_one_or_none()

    async def get_by_code(self, template_code: str, factory_id: int) -> Optional[CreateTemplate]:
        """Get template by code and factory."""
        async with self.session_factory() as session:
            stmt = (
                select(CreateTemplate)
                .where(
                    and_(
                        CreateTemplate.template_code == template_code,
                        CreateTemplate.factory_id == factory_id
                    )
                )
            )
            result = await session.execute(stmt)
            return result.scalar_one_or_none()

    async def get_by_name(self, template_name: str, factory_id: int) -> Optional[CreateTemplate]:
        """Get template by name and factory."""
        async with self.session_factory() as session:
            stmt = (
                select(CreateTemplate)
                .where(
                    and_(
                        CreateTemplate.template_name == template_name,
                        CreateTemplate.factory_id == factory_id
                    )
                )
            )
            result = await session.execute(stmt)
            return result.scalar_one_or_none()

    async def get_by_part_type(self, part_type: str, factory_id: int, status: Optional[TemplateStatus] = None) -> List[CreateTemplate]:
        """Get templates by part type."""
        async with self.session_factory() as session:
            conditions = [
                CreateTemplate.part_type == part_type,
                CreateTemplate.factory_id == factory_id
            ]
            
            if status:
                conditions.append(CreateTemplate.status == status)
            
            stmt = (
                select(CreateTemplate)
                .where(and_(*conditions))
                .order_by(CreateTemplate.template_name)
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_by_category(self, category: str, factory_id: int) -> List[CreateTemplate]:
        """Get templates by category."""
        async with self.session_factory() as session:
            stmt = (
                select(CreateTemplate)
                .where(
                    and_(
                        CreateTemplate.category == category,
                        CreateTemplate.factory_id == factory_id
                    )
                )
                .order_by(CreateTemplate.template_name)
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_by_status(self, status: TemplateStatus, factory_id: int, skip: int = 0, limit: int = 100) -> List[CreateTemplate]:
        """Get templates by status."""
        async with self.session_factory() as session:
            stmt = (
                select(CreateTemplate)
                .where(
                    and_(
                        CreateTemplate.status == status,
                        CreateTemplate.factory_id == factory_id
                    )
                )
                .order_by(CreateTemplate.created_at.desc())
                .offset(skip)
                .limit(limit)
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_by_creator(self, creator_user_id: int, factory_id: int) -> List[CreateTemplate]:
        """Get templates created by a specific user."""
        async with self.session_factory() as session:
            stmt = (
                select(CreateTemplate)
                .where(
                    and_(
                        CreateTemplate.created_by_user_id == creator_user_id,
                        CreateTemplate.factory_id == factory_id
                    )
                )
                .order_by(CreateTemplate.created_at.desc())
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_by_tags(self, tags: List[str], factory_id: int, match_all: bool = False) -> List[CreateTemplate]:
        """Get templates by tags."""
        async with self.session_factory() as session:
            conditions = [CreateTemplate.factory_id == factory_id]
            
            if match_all:
                # All tags must be present
                for tag in tags:
                    conditions.append(CreateTemplate.tags.contains([tag]))
            else:
                # Any tag can be present
                tag_conditions = [CreateTemplate.tags.contains([tag]) for tag in tags]
                conditions.append(or_(*tag_conditions))
            
            stmt = (
                select(CreateTemplate)
                .where(and_(*conditions))
                .order_by(CreateTemplate.template_name)
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def search_templates(
        self,
        factory_id: int,
        part_type: Optional[str] = None,
        template_name: Optional[str] = None,
        template_code: Optional[str] = None,
        search_params: Optional[Dict[str, Any]] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[CreateTemplate]:
        """Search templates with flexible parameters."""
        async with self.session_factory() as session:
            conditions = [CreateTemplate.factory_id == factory_id]
            
            # Core search conditions
            if part_type:
                conditions.append(CreateTemplate.part_type == part_type)
            
            if template_name:
                conditions.append(CreateTemplate.template_name.ilike(f"%{template_name}%"))
            
            if template_code:
                conditions.append(CreateTemplate.template_code.ilike(f"%{template_code}%"))
            
            # Additional search parameters
            if search_params:
                if search_term := search_params.get("search_term"):
                    conditions.append(
                        or_(
                            CreateTemplate.template_name.ilike(f"%{search_term}%"),
                            CreateTemplate.template_code.ilike(f"%{search_term}%"),
                            CreateTemplate.description.ilike(f"%{search_term}%")
                        )
                    )
                
                if status := search_params.get("status"):
                    conditions.append(CreateTemplate.status == status)
                
                if category := search_params.get("category"):
                    conditions.append(CreateTemplate.category == category)
                
                if is_public := search_params.get("is_public"):
                    conditions.append(CreateTemplate.is_public == is_public)
                
                if is_system := search_params.get("is_system_template"):
                    conditions.append(CreateTemplate.is_system_template == is_system)
                
                if access_level := search_params.get("access_level"):
                    conditions.append(CreateTemplate.access_level == access_level)
                
                if creator_id := search_params.get("created_by_user_id"):
                    conditions.append(CreateTemplate.created_by_user_id == creator_id)
                
                if start_date := search_params.get("start_date"):
                    conditions.append(CreateTemplate.created_at >= start_date)
                
                if end_date := search_params.get("end_date"):
                    conditions.append(CreateTemplate.created_at <= end_date)
            
            stmt = (
                select(CreateTemplate)
                .where(and_(*conditions))
                .order_by(CreateTemplate.template_name)
                .offset(skip)
                .limit(limit)
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_public_templates(self, factory_id: int, part_type: Optional[str] = None) -> List[CreateTemplate]:
        """Get all public templates."""
        async with self.session_factory() as session:
            conditions = [
                CreateTemplate.factory_id == factory_id,
                CreateTemplate.is_public == True,
                CreateTemplate.status == TemplateStatus.ACTIVE
            ]
            
            if part_type:
                conditions.append(CreateTemplate.part_type == part_type)
            
            stmt = (
                select(CreateTemplate)
                .where(and_(*conditions))
                .order_by(CreateTemplate.template_name)
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_system_templates(self, factory_id: int) -> List[CreateTemplate]:
        """Get system templates."""
        async with self.session_factory() as session:
            stmt = (
                select(CreateTemplate)
                .where(
                    and_(
                        CreateTemplate.factory_id == factory_id,
                        CreateTemplate.is_system_template == True
                    )
                )
                .order_by(CreateTemplate.template_name)
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_accessible_templates(
        self, 
        factory_id: int, 
        user_access_level: str, 
        part_type: Optional[str] = None
    ) -> List[CreateTemplate]:
        """Get templates accessible to user based on access level."""
        async with self.session_factory() as session:
            access_hierarchy = {"standard": 1, "restricted": 2, "admin": 3}
            user_level = access_hierarchy.get(user_access_level, 1)
            
            # Get access levels that user can access
            accessible_levels = [level for level, value in access_hierarchy.items() if value <= user_level]
            
            conditions = [
                CreateTemplate.factory_id == factory_id,
                CreateTemplate.status == TemplateStatus.ACTIVE,
                CreateTemplate.access_level.in_(accessible_levels)
            ]
            
            if part_type:
                conditions.append(CreateTemplate.part_type == part_type)
            
            stmt = (
                select(CreateTemplate)
                .where(and_(*conditions))
                .order_by(CreateTemplate.template_name)
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_templates_by_parent(self, parent_template_id: int) -> List[CreateTemplate]:
        """Get child templates of a parent template."""
        async with self.session_factory() as session:
            stmt = (
                select(CreateTemplate)
                .where(CreateTemplate.parent_template_id == parent_template_id)
                .order_by(CreateTemplate.template_name)
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_most_used_templates(
        self, 
        factory_id: int, 
        part_type: Optional[str] = None, 
        limit: int = 10
    ) -> List[CreateTemplate]:
        """Get most frequently used templates."""
        async with self.session_factory() as session:
            conditions = [
                CreateTemplate.factory_id == factory_id,
                CreateTemplate.status == TemplateStatus.ACTIVE
            ]
            
            if part_type:
                conditions.append(CreateTemplate.part_type == part_type)
            
            stmt = (
                select(CreateTemplate)
                .where(and_(*conditions))
                .order_by(CreateTemplate.usage_count.desc())
                .limit(limit)
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_recently_used_templates(
        self, 
        factory_id: int, 
        user_id: Optional[int] = None, 
        limit: int = 10
    ) -> List[CreateTemplate]:
        """Get recently used templates."""
        async with self.session_factory() as session:
            conditions = [
                CreateTemplate.factory_id == factory_id,
                CreateTemplate.last_used_at.is_not(None)
            ]
            
            if user_id:
                conditions.append(
                    or_(
                        CreateTemplate.created_by_user_id == user_id,
                        CreateTemplate.updated_by_user_id == user_id
                    )
                )
            
            stmt = (
                select(CreateTemplate)
                .where(and_(*conditions))
                .order_by(CreateTemplate.last_used_at.desc())
                .limit(limit)
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_recently_created_templates(self, factory_id: int, limit: int = 10) -> List[CreateTemplate]:
        """Get recently created templates."""
        async with self.session_factory() as session:
            stmt = (
                select(CreateTemplate)
                .where(CreateTemplate.factory_id == factory_id)
                .order_by(CreateTemplate.created_at.desc())
                .limit(limit)
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_templates_by_date_range(
        self,
        factory_id: int,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        date_field: str = "created_at"
    ) -> List[CreateTemplate]:
        """Get templates within a date range."""
        async with self.session_factory() as session:
            conditions = [CreateTemplate.factory_id == factory_id]
            
            date_column = getattr(CreateTemplate, date_field)
            
            if start_date:
                conditions.append(date_column >= start_date)
            if end_date:
                conditions.append(date_column <= end_date)
            
            stmt = (
                select(CreateTemplate)
                .where(and_(*conditions))
                .order_by(date_column.desc())
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def update(self, template: CreateTemplate) -> CreateTemplate:
        """Update an existing template."""
        async with self.session_factory() as session:
            await session.merge(template)
            await session.commit()
            await session.refresh(template)
            return template

    async def delete(self, template_id: int) -> bool:
        """Delete a template."""
        async with self.session_factory() as session:
            stmt = delete(CreateTemplate).where(CreateTemplate.id == template_id)
            result = await session.execute(stmt)
            await session.commit()
            return result.rowcount > 0

    async def bulk_create(self, templates: List[CreateTemplate]) -> List[CreateTemplate]:
        """Create multiple templates."""
        async with self.session_factory() as session:
            session.add_all(templates)
            await session.commit()
            for template in templates:
                await session.refresh(template)
            return templates

    async def bulk_update_status(
        self, 
        template_ids: List[int], 
        status: TemplateStatus
    ) -> List[CreateTemplate]:
        """Bulk update status for multiple templates."""
        async with self.session_factory() as session:
            update_data = {
                "status": status,
                "updated_at": datetime.now()
            }
            
            stmt = (
                update(CreateTemplate)
                .where(CreateTemplate.id.in_(template_ids))
                .values(**update_data)
            )
            await session.execute(stmt)
            await session.commit()
            
            # Return updated templates
            return await self.get_by_ids(template_ids)

    async def duplicate_template(
        self, 
        template_id: int, 
        new_name: str, 
        new_code: str, 
        creator_user_id: int
    ) -> CreateTemplate:
        """Duplicate an existing template."""
        async with self.session_factory() as session:
            # Get original template
            original = await self.get_by_id(template_id)
            if not original:
                raise ValueError(f"Template with ID {template_id} not found")
            
            # Create duplicate
            duplicate = original.clone_template(new_name, new_code)
            duplicate.created_by_user_id = creator_user_id
            duplicate.created_at = datetime.now()
            duplicate.updated_at = datetime.now()
            
            return await self.create(duplicate)

    async def get_template_statistics(self, factory_id: int) -> Dict[str, Any]:
        """Get template statistics."""
        async with self.session_factory() as session:
            stmt = (
                select(
                    func.count(CreateTemplate.id).label("total"),
                    func.sum(func.case((CreateTemplate.status == TemplateStatus.ACTIVE, 1), else_=0)).label("active"),
                    func.sum(func.case((CreateTemplate.status == TemplateStatus.INACTIVE, 1), else_=0)).label("inactive"),
                    func.sum(func.case((CreateTemplate.status == TemplateStatus.DRAFT, 1), else_=0)).label("draft"),
                    func.sum(func.case((CreateTemplate.status == TemplateStatus.ARCHIVED, 1), else_=0)).label("archived"),
                    func.sum(func.case((CreateTemplate.is_public == True, 1), else_=0)).label("public"),
                    func.sum(func.case((CreateTemplate.is_system_template == True, 1), else_=0)).label("system"),
                    func.sum(CreateTemplate.usage_count).label("total_usage"),
                    func.avg(CreateTemplate.usage_count).label("avg_usage")
                )
                .where(CreateTemplate.factory_id == factory_id)
            )
            result = await session.execute(stmt)
            row = result.first()
            
            return {
                "total": row.total or 0,
                "active": row.active or 0,
                "inactive": row.inactive or 0,
                "draft": row.draft or 0,
                "archived": row.archived or 0,
                "public": row.public or 0,
                "system": row.system or 0,
                "total_usage": row.total_usage or 0,
                "avg_usage": float(row.avg_usage or 0)
            }

    async def get_usage_statistics(
        self, 
        factory_id: int, 
        start_date: Optional[datetime] = None, 
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get template usage statistics."""
        # Basic implementation - can be expanded with actual usage tracking
        return {
            "period_start": start_date,
            "period_end": end_date,
            "total_uses": 0,
            "unique_templates_used": 0,
            "avg_uses_per_template": 0,
            "most_used_template": None
        }

    async def count_by_status(self, factory_id: int) -> Dict[str, int]:
        """Count templates by status."""
        async with self.session_factory() as session:
            stmt = (
                select(
                    CreateTemplate.status,
                    func.count(CreateTemplate.id).label("count")
                )
                .where(CreateTemplate.factory_id == factory_id)
                .group_by(CreateTemplate.status)
            )
            result = await session.execute(stmt)
            
            counts = {status.value: 0 for status in TemplateStatus}
            for row in result:
                counts[row.status.value] = row.count
            
            return counts

    async def count_by_part_type(self, factory_id: int) -> Dict[str, int]:
        """Count templates by part type."""
        async with self.session_factory() as session:
            stmt = (
                select(
                    CreateTemplate.part_type,
                    func.count(CreateTemplate.id).label("count")
                )
                .where(CreateTemplate.factory_id == factory_id)
                .group_by(CreateTemplate.part_type)
            )
            result = await session.execute(stmt)
            
            return {row.part_type: row.count for row in result}

    async def count_by_category(self, factory_id: int) -> Dict[str, int]:
        """Count templates by category."""
        async with self.session_factory() as session:
            stmt = (
                select(
                    CreateTemplate.category,
                    func.count(CreateTemplate.id).label("count")
                )
                .where(
                    and_(
                        CreateTemplate.factory_id == factory_id,
                        CreateTemplate.category.is_not(None)
                    )
                )
                .group_by(CreateTemplate.category)
            )
            result = await session.execute(stmt)
            
            return {row.category: row.count for row in result}

    async def get_template_versions(self, template_code: str, factory_id: int) -> List[CreateTemplate]:
        """Get all versions of a template by code."""
        async with self.session_factory() as session:
            stmt = (
                select(CreateTemplate)
                .where(
                    and_(
                        CreateTemplate.template_code == template_code,
                        CreateTemplate.factory_id == factory_id
                    )
                )
                .order_by(CreateTemplate.version.desc())
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_latest_version(self, template_code: str, factory_id: int) -> Optional[CreateTemplate]:
        """Get the latest version of a template."""
        versions = await self.get_template_versions(template_code, factory_id)
        return versions[0] if versions else None

    async def search_by_json_field(
        self,
        factory_id: int,
        json_field: str,
        search_criteria: Dict[str, Any],
        skip: int = 0,
        limit: int = 100
    ) -> List[CreateTemplate]:
        """Search templates by JSON field content."""
        async with self.session_factory() as session:
            conditions = [CreateTemplate.factory_id == factory_id]
            
            # Get the JSON column
            json_column = getattr(CreateTemplate, json_field)
            
            # Build JSON search conditions
            for key, value in search_criteria.items():
                conditions.append(json_column[key].astext == str(value))
            
            stmt = (
                select(CreateTemplate)
                .where(and_(*conditions))
                .order_by(CreateTemplate.template_name)
                .offset(skip)
                .limit(limit)
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def get_templates_with_validation_rules(self, factory_id: int) -> List[CreateTemplate]:
        """Get templates that have validation rules defined."""
        async with self.session_factory() as session:
            stmt = (
                select(CreateTemplate)
                .where(
                    and_(
                        CreateTemplate.factory_id == factory_id,
                        CreateTemplate.validation_rules.is_not(None)
                    )
                )
                .order_by(CreateTemplate.template_name)
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())

    async def archive_old_templates(
        self, 
        factory_id: int, 
        older_than_days: int, 
        exclude_system: bool = True
    ) -> int:
        """Archive templates older than specified days."""
        async with self.session_factory() as session:
            cutoff_date = datetime.now() - timedelta(days=older_than_days)
            
            conditions = [
                CreateTemplate.factory_id == factory_id,
                CreateTemplate.created_at < cutoff_date,
                CreateTemplate.status != TemplateStatus.ARCHIVED
            ]
            
            if exclude_system:
                conditions.append(CreateTemplate.is_system_template == False)
            
            stmt = (
                update(CreateTemplate)
                .where(and_(*conditions))
                .values(status=TemplateStatus.ARCHIVED, updated_at=datetime.now())
            )
            result = await session.execute(stmt)
            await session.commit()
            return result.rowcount

    async def cleanup_unused_templates(
        self, 
        factory_id: int, 
        unused_for_days: int
    ) -> int:
        """Clean up templates that haven't been used for specified days."""
        async with self.session_factory() as session:
            cutoff_date = datetime.now() - timedelta(days=unused_for_days)
            
            conditions = [
                CreateTemplate.factory_id == factory_id,
                CreateTemplate.is_system_template == False,
                or_(
                    CreateTemplate.last_used_at < cutoff_date,
                    CreateTemplate.last_used_at.is_(None)
                ),
                CreateTemplate.usage_count == 0
            ]
            
            stmt = (
                update(CreateTemplate)
                .where(and_(*conditions))
                .values(status=TemplateStatus.ARCHIVED, updated_at=datetime.now())
            )
            result = await session.execute(stmt)
            await session.commit()
            return result.rowcount

    async def get_by_ids(self, template_ids: List[int]) -> List[CreateTemplate]:
        """Get multiple templates by IDs."""
        async with self.session_factory() as session:
            stmt = (
                select(CreateTemplate)
                .where(CreateTemplate.id.in_(template_ids))
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())