from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class OrderCraftInstanceStatusDTO(str, Enum):
    """OrderCraftInstance status enumeration for DTOs."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    ON_HOLD = "on_hold"
    SKIPPED = "skipped"
    FAILED = "failed"


class OrderCraftInstanceBaseDTO(BaseModel):
    """Base order craft instance DTO."""
    order_no: str = Field(..., min_length=1, max_length=100, description="订单号")
    order_part_no: str = Field(..., min_length=1, max_length=100, description="订单部位号")
    part_type: str = Field(..., max_length=50, description="部位类型")
    craft_code: str = Field(..., max_length=50, description="工艺代码")
    sequence_order: int = Field(default=0, ge=0, description="在部位工艺流程中的顺序")
    is_required: bool = Field(default=True, description="是否必需的工艺步骤")
    is_active: bool = Field(default=True, description="是否激活")
    planned_start_date: Optional[datetime] = Field(None, description="计划开始时间")
    planned_end_date: Optional[datetime] = Field(None, description="计划完成时间")
    estimated_duration_minutes: Optional[int] = Field(None, ge=0, description="预估耗时(分钟)")
    assigned_user_id: Optional[int] = Field(None, description="分配的用户ID")
    supervisor_user_id: Optional[int] = Field(None, description="负责人用户ID")
    notes: Optional[str] = Field(None, max_length=2000, description="备注")


class OrderCraftInstanceCreateDTO(OrderCraftInstanceBaseDTO):
    """DTO for creating a new order craft instance."""
    order_id: int = Field(..., description="订单ID")
    order_part_id: int = Field(..., description="订单部位ID")
    order_craft_id: int = Field(..., description="订单工艺ID")


class OrderCraftInstanceUpdateDTO(BaseModel):
    """DTO for updating an order craft instance."""
    status: Optional[OrderCraftInstanceStatusDTO] = Field(None, description="实例状态")
    progress_percentage: Optional[int] = Field(None, ge=0, le=100, description="完成百分比")
    sequence_order: Optional[int] = Field(None, ge=0, description="序列顺序")
    is_required: Optional[bool] = Field(None, description="是否必需")
    is_active: Optional[bool] = Field(None, description="是否激活")
    planned_start_date: Optional[datetime] = Field(None, description="计划开始时间")
    planned_end_date: Optional[datetime] = Field(None, description="计划完成时间")
    estimated_duration_minutes: Optional[int] = Field(None, ge=0, description="预估耗时(分钟)")
    assigned_user_id: Optional[int] = Field(None, description="分配的用户ID")
    supervisor_user_id: Optional[int] = Field(None, description="负责人用户ID")
    quality_score: Optional[int] = Field(None, ge=0, le=100, description="质量分数")
    notes: Optional[str] = Field(None, max_length=2000, description="备注")
    completion_notes: Optional[str] = Field(None, max_length=2000, description="完成备注")


class OrderCraftInstanceResponseDTO(OrderCraftInstanceBaseDTO):
    """DTO for order craft instance response."""
    id: int = Field(..., description="实例ID")
    factory_id: int = Field(..., description="工厂ID")
    order_id: int = Field(..., description="订单ID")
    order_part_id: int = Field(..., description="订单部位ID")
    order_craft_id: int = Field(..., description="订单工艺ID")
    status: OrderCraftInstanceStatusDTO = Field(..., description="实例状态")
    progress_percentage: int = Field(..., description="完成百分比")
    actual_start_date: Optional[datetime] = Field(None, description="实际开始时间")
    actual_end_date: Optional[datetime] = Field(None, description="实际完成时间")
    actual_duration_minutes: Optional[int] = Field(None, description="实际耗时(分钟)")
    quality_score: Optional[int] = Field(None, description="质量分数")
    rework_count: int = Field(default=0, description="返工次数")
    defect_count: int = Field(default=0, description="次品数量")
    completion_notes: Optional[str] = Field(None, description="完成备注")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    # User information
    assigned_user_name: Optional[str] = Field(None, description="分配用户名")
    supervisor_name: Optional[str] = Field(None, description="负责人姓名")
    
    # Duration and timing calculations
    duration_minutes: Optional[int] = Field(None, description="持续时间(分钟)")
    is_overdue: bool = Field(default=False, description="是否逾期")
    
    class Config:
        from_attributes = True


class OrderCraftInstanceDetailDTO(OrderCraftInstanceResponseDTO):
    """DTO for detailed order craft instance with related data."""
    # Order information
    order_skc_no: Optional[str] = Field(None, description="款号")
    order_status: Optional[str] = Field(None, description="订单状态")
    
    # Part information
    part_name: Optional[str] = Field(None, description="部位名称")
    part_sequence: Optional[int] = Field(None, description="部位序号")
    part_total_quantity: Optional[int] = Field(None, description="部位总量")
    
    # Craft information
    craft_name: Optional[str] = Field(None, description="工艺名称")
    craft_estimated_duration: Optional[int] = Field(None, description="工艺预估时长")
    
    # Route instances count
    total_route_instances: int = Field(default=0, description="总路线实例数")
    completed_route_instances: int = Field(default=0, description="已完成路线实例数")
    route_completion_percentage: float = Field(default=0.0, description="路线完成百分比")


class OrderCraftInstanceListDTO(BaseModel):
    """DTO for order craft instance list response."""
    instances: List[OrderCraftInstanceResponseDTO] = Field(..., description="实例列表")
    total: int = Field(..., description="总数量")


class OrderCraftInstanceSearchDTO(BaseModel):
    """DTO for order craft instance search criteria."""
    search_term: Optional[str] = Field(None, description="搜索关键词")
    status: Optional[OrderCraftInstanceStatusDTO] = Field(None, description="状态")
    part_type: Optional[str] = Field(None, description="部位类型")
    craft_code: Optional[str] = Field(None, description="工艺代码")
    assigned_user_id: Optional[int] = Field(None, description="分配用户ID")
    supervisor_id: Optional[int] = Field(None, description="负责人ID")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")
    date_field: str = Field(default="created_at", description="日期字段")
    is_overdue: Optional[bool] = Field(None, description="是否逾期")


class OrderCraftInstanceStatusUpdateDTO(BaseModel):
    """DTO for updating order craft instance status."""
    status: OrderCraftInstanceStatusDTO = Field(..., description="新状态")
    notes: Optional[str] = Field(None, description="状态变更备注")
    completion_notes: Optional[str] = Field(None, description="完成备注")


class OrderCraftInstanceAssignmentDTO(BaseModel):
    """DTO for assigning order craft instance."""
    assigned_user_id: Optional[int] = Field(None, description="分配用户ID")
    supervisor_user_id: Optional[int] = Field(None, description="负责人用户ID")
    notes: Optional[str] = Field(None, description="分配备注")


class OrderCraftInstanceProgressDTO(BaseModel):
    """DTO for updating order craft instance progress."""
    progress_percentage: int = Field(..., ge=0, le=100, description="完成百分比")
    quality_score: Optional[int] = Field(None, ge=0, le=100, description="质量分数")
    notes: Optional[str] = Field(None, description="进度备注")


class OrderCraftInstanceQualityDTO(BaseModel):
    """DTO for quality-related updates."""
    quality_score: Optional[int] = Field(None, ge=0, le=100, description="质量分数")
    rework_count: Optional[int] = Field(None, ge=0, description="返工次数")
    defect_count: Optional[int] = Field(None, ge=0, description="次品数量")
    notes: Optional[str] = Field(None, description="质量备注")


class OrderCraftInstanceBulkCreateDTO(BaseModel):
    """DTO for bulk creating order craft instances."""
    order_no: str = Field(..., description="订单号")
    order_part_no: str = Field(..., description="订单部位号")
    instances: List[OrderCraftInstanceCreateDTO] = Field(..., min_items=1, description="实例列表")


class OrderCraftInstanceBulkUpdateDTO(BaseModel):
    """DTO for bulk updating order craft instances."""
    instance_ids: List[int] = Field(..., min_items=1, description="实例ID列表")
    update_data: OrderCraftInstanceUpdateDTO = Field(..., description="更新数据")


class OrderCraftInstanceStatisticsDTO(BaseModel):
    """DTO for order craft instance statistics."""
    total: int = Field(..., description="总数量")
    completed: int = Field(..., description="已完成数量")
    in_progress: int = Field(..., description="进行中数量")
    pending: int = Field(..., description="待处理数量")
    on_hold: int = Field(..., description="暂停数量")
    skipped: int = Field(..., description="跳过数量")
    failed: int = Field(..., description="失败数量")
    completion_rate: float = Field(..., description="完成率")
    avg_progress: float = Field(..., description="平均进度")
    avg_duration_minutes: float = Field(..., description="平均持续时间(分钟)")
    avg_quality_score: float = Field(..., description="平均质量分数")
    total_rework: int = Field(..., description="总返工次数")
    total_defects: int = Field(..., description="总次品数量")
    overdue_count: int = Field(..., description="逾期数量")
    on_time_rate: float = Field(..., description="按时完成率")


class OrderCraftInstanceWorkflowDTO(BaseModel):
    """DTO for order craft instance workflow status."""
    order_no: str = Field(..., description="订单号")
    order_part_no: str = Field(..., description="订单部位号")
    part_type: str = Field(..., description="部位类型")
    workflow_instances: List[OrderCraftInstanceResponseDTO] = Field(..., description="工作流实例列表")
    current_step: Optional[OrderCraftInstanceResponseDTO] = Field(None, description="当前步骤")
    next_steps: List[OrderCraftInstanceResponseDTO] = Field(..., description="下一步骤列表")
    total_steps: int = Field(..., description="总步骤数")
    completed_steps: int = Field(..., description="已完成步骤数")
    overall_progress: float = Field(..., description="整体进度")


class OrderCraftInstancePerformanceDTO(BaseModel):
    """DTO for order craft instance performance metrics."""
    period_start: Optional[datetime] = Field(None, description="统计开始时间")
    period_end: Optional[datetime] = Field(None, description="统计结束时间")
    statistics: OrderCraftInstanceStatisticsDTO = Field(..., description="统计数据")
    part_type_breakdown: dict = Field(..., description="按部位类型分解")
    craft_breakdown: dict = Field(..., description="按工艺分解")
    user_performance: dict = Field(..., description="用户绩效")


class OrderCraftInstanceOperationResultDTO(BaseModel):
    """DTO for order craft instance operation results."""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="结果消息")
    instance_id: Optional[int] = Field(None, description="实例ID")
    instance_ids: Optional[List[int]] = Field(None, description="实例ID列表")
    details: Optional[dict] = Field(None, description="详细信息")


class OrderCraftInstanceDashboardDTO(BaseModel):
    """DTO for order craft instance dashboard data."""
    statistics: OrderCraftInstanceStatisticsDTO = Field(..., description="总体统计")
    overdue_instances: List[OrderCraftInstanceResponseDTO] = Field(..., description="逾期实例")
    in_progress_instances: List[OrderCraftInstanceResponseDTO] = Field(..., description="进行中实例")
    recent_completions: List[OrderCraftInstanceResponseDTO] = Field(..., description="最近完成")
    performance_by_craft: dict = Field(..., description="按工艺的绩效")
    performance_by_part_type: dict = Field(..., description="按部位类型的绩效")
    quality_trends: dict = Field(..., description="质量趋势")


class OrderCraftInstanceTimelineDTO(BaseModel):
    """DTO for order craft instance timeline."""
    instance_id: int = Field(..., description="实例ID")
    events: List[dict] = Field(..., description="时间线事件")
    duration_breakdown: dict = Field(..., description="时长分解")
    milestones: List[dict] = Field(..., description="里程碑")
    
    
class OrderCraftInstanceComparisonDTO(BaseModel):
    """DTO for comparing order craft instances."""
    baseline_instance: OrderCraftInstanceResponseDTO = Field(..., description="基准实例")
    comparison_instances: List[OrderCraftInstanceResponseDTO] = Field(..., description="对比实例列表")
    metrics: dict = Field(..., description="对比指标")
    insights: List[str] = Field(..., description="对比洞察")