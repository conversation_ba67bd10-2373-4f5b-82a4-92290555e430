from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, ConfigDict
from src.domain.entities.create_template import TemplateStatus


class CreateTemplateCreateDTO(BaseModel):
    """DTO for creating a new template."""
    model_config = ConfigDict(from_attributes=True)
    
    factory_id: int = Field(..., description="工厂ID")
    part_type: str = Field(..., max_length=50, description="部位类型")
    template_name: str = Field(..., max_length=100, description="模板名称")
    template_code: str = Field(..., max_length=50, description="模板代码")
    description: Optional[str] = Field(None, description="模板描述")
    version: str = Field("1.0.0", max_length=20, description="模板版本")
    
    # JSON data fields
    template_data: Dict[str, Any] = Field(..., description="模板主要数据")
    configuration: Optional[Dict[str, Any]] = Field(None, description="配置数据")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")
    validation_rules: Optional[Dict[str, Any]] = Field(None, description="验证规则")
    ui_config: Optional[Dict[str, Any]] = Field(None, description="UI配置")
    
    # Template hierarchy and categorization
    parent_template_id: Optional[int] = Field(None, description="父模板ID")
    category: Optional[str] = Field(None, max_length=50, description="模板分类")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    
    # Access control
    is_public: bool = Field(True, description="是否公开")
    is_system_template: bool = Field(False, description="是否系统模板")
    access_level: str = Field("standard", description="访问级别")
    
    # Creator
    created_by_user_id: int = Field(..., description="创建者用户ID")


class CreateTemplateUpdateDTO(BaseModel):
    """DTO for updating a template."""
    model_config = ConfigDict(from_attributes=True)
    
    template_name: Optional[str] = Field(None, max_length=100, description="模板名称")
    description: Optional[str] = Field(None, description="模板描述")
    version: Optional[str] = Field(None, max_length=20, description="模板版本")
    status: Optional[TemplateStatus] = Field(None, description="模板状态")
    
    # JSON data fields
    template_data: Optional[Dict[str, Any]] = Field(None, description="模板主要数据")
    configuration: Optional[Dict[str, Any]] = Field(None, description="配置数据")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")
    validation_rules: Optional[Dict[str, Any]] = Field(None, description="验证规则")
    ui_config: Optional[Dict[str, Any]] = Field(None, description="UI配置")
    
    # Template hierarchy and categorization
    parent_template_id: Optional[int] = Field(None, description="父模板ID")
    category: Optional[str] = Field(None, max_length=50, description="模板分类")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    
    # Access control
    is_public: Optional[bool] = Field(None, description="是否公开")
    access_level: Optional[str] = Field(None, description="访问级别")
    
    # Updated by
    updated_by_user_id: Optional[int] = Field(None, description="最后更新者用户ID")


class CreateTemplateResponseDTO(BaseModel):
    """DTO for template response."""
    model_config = ConfigDict(from_attributes=True)
    
    id: int = Field(..., description="模板ID")
    factory_id: int = Field(..., description="工厂ID")
    part_type: str = Field(..., description="部位类型")
    template_name: str = Field(..., description="模板名称")
    template_code: str = Field(..., description="模板代码")
    description: Optional[str] = Field(None, description="模板描述")
    version: str = Field(..., description="模板版本")
    status: TemplateStatus = Field(..., description="模板状态")
    
    # JSON data fields
    template_data: Dict[str, Any] = Field(..., description="模板主要数据")
    configuration: Optional[Dict[str, Any]] = Field(None, description="配置数据")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")
    validation_rules: Optional[Dict[str, Any]] = Field(None, description="验证规则")
    ui_config: Optional[Dict[str, Any]] = Field(None, description="UI配置")
    
    # Template hierarchy and categorization
    parent_template_id: Optional[int] = Field(None, description="父模板ID")
    category: Optional[str] = Field(None, description="模板分类")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    
    # Access control
    is_public: bool = Field(..., description="是否公开")
    is_system_template: bool = Field(..., description="是否系统模板")
    access_level: str = Field(..., description="访问级别")
    
    # Users
    created_by_user_id: int = Field(..., description="创建者用户ID")
    updated_by_user_id: Optional[int] = Field(None, description="最后更新者用户ID")
    
    # Usage statistics
    usage_count: int = Field(..., description="使用次数")
    last_used_at: Optional[datetime] = Field(None, description="最后使用时间")
    
    # Audit fields
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class CreateTemplateListDTO(BaseModel):
    """DTO for template list item (simplified)."""
    model_config = ConfigDict(from_attributes=True)
    
    id: int = Field(..., description="模板ID")
    part_type: str = Field(..., description="部位类型")
    template_name: str = Field(..., description="模板名称")
    template_code: str = Field(..., description="模板代码")
    description: Optional[str] = Field(None, description="模板描述")
    version: str = Field(..., description="模板版本")
    status: TemplateStatus = Field(..., description="模板状态")
    category: Optional[str] = Field(None, description="模板分类")
    is_public: bool = Field(..., description="是否公开")
    is_system_template: bool = Field(..., description="是否系统模板")
    usage_count: int = Field(..., description="使用次数")
    last_used_at: Optional[datetime] = Field(None, description="最后使用时间")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class CreateTemplateSearchDTO(BaseModel):
    """DTO for template search parameters."""
    model_config = ConfigDict(from_attributes=True)
    
    factory_id: int = Field(..., description="工厂ID")
    part_type: Optional[str] = Field(None, description="部位类型")
    template_name: Optional[str] = Field(None, description="模板名称")
    template_code: Optional[str] = Field(None, description="模板代码")
    search_term: Optional[str] = Field(None, description="搜索关键词")
    status: Optional[TemplateStatus] = Field(None, description="模板状态")
    category: Optional[str] = Field(None, description="模板分类")
    is_public: Optional[bool] = Field(None, description="是否公开")
    is_system_template: Optional[bool] = Field(None, description="是否系统模板")
    access_level: Optional[str] = Field(None, description="访问级别")
    created_by_user_id: Optional[int] = Field(None, description="创建者用户ID")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")
    skip: int = Field(0, ge=0, description="跳过条数")
    limit: int = Field(100, ge=1, le=1000, description="返回条数")


class CreateTemplateDuplicateDTO(BaseModel):
    """DTO for duplicating a template."""
    model_config = ConfigDict(from_attributes=True)
    
    template_id: int = Field(..., description="要复制的模板ID")
    new_template_name: str = Field(..., max_length=100, description="新模板名称")
    new_template_code: str = Field(..., max_length=50, description="新模板代码")
    creator_user_id: int = Field(..., description="创建者用户ID")


class CreateTemplateDataUpdateDTO(BaseModel):
    """DTO for updating template data specifically."""
    model_config = ConfigDict(from_attributes=True)
    
    template_data: Optional[Dict[str, Any]] = Field(None, description="模板主要数据")
    configuration: Optional[Dict[str, Any]] = Field(None, description="配置数据")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")
    validation_rules: Optional[Dict[str, Any]] = Field(None, description="验证规则")
    ui_config: Optional[Dict[str, Any]] = Field(None, description="UI配置")
    updated_by_user_id: Optional[int] = Field(None, description="更新者用户ID")


class CreateTemplateStatusUpdateDTO(BaseModel):
    """DTO for updating template status."""
    model_config = ConfigDict(from_attributes=True)
    
    status: TemplateStatus = Field(..., description="新状态")
    updated_by_user_id: Optional[int] = Field(None, description="更新者用户ID")


class CreateTemplateBulkStatusUpdateDTO(BaseModel):
    """DTO for bulk status update."""
    model_config = ConfigDict(from_attributes=True)
    
    template_ids: List[int] = Field(..., description="模板ID列表")
    status: TemplateStatus = Field(..., description="新状态")
    updated_by_user_id: Optional[int] = Field(None, description="更新者用户ID")


class CreateTemplateStatisticsDTO(BaseModel):
    """DTO for template statistics."""
    model_config = ConfigDict(from_attributes=True)
    
    total: int = Field(..., description="总数")
    active: int = Field(..., description="活跃数")
    inactive: int = Field(..., description="非活跃数")
    draft: int = Field(..., description="草稿数")
    archived: int = Field(..., description="归档数")
    public: int = Field(..., description="公开数")
    system: int = Field(..., description="系统模板数")
    total_usage: int = Field(..., description="总使用次数")
    avg_usage: float = Field(..., description="平均使用次数")


class CreateTemplateUsageStatisticsDTO(BaseModel):
    """DTO for template usage statistics."""
    model_config = ConfigDict(from_attributes=True)
    
    period_start: Optional[datetime] = Field(None, description="统计开始时间")
    period_end: Optional[datetime] = Field(None, description="统计结束时间")
    total_uses: int = Field(..., description="总使用次数")
    unique_templates_used: int = Field(..., description="使用的唯一模板数")
    avg_uses_per_template: float = Field(..., description="每个模板平均使用次数")
    most_used_template: Optional[str] = Field(None, description="最常用模板")


class CreateTemplateVersionDTO(BaseModel):
    """DTO for template version information."""
    model_config = ConfigDict(from_attributes=True)
    
    id: int = Field(..., description="模板ID")
    template_code: str = Field(..., description="模板代码")
    template_name: str = Field(..., description="模板名称")
    version: str = Field(..., description="版本号")
    status: TemplateStatus = Field(..., description="模板状态")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class CreateTemplateJsonSearchDTO(BaseModel):
    """DTO for JSON field search."""
    model_config = ConfigDict(from_attributes=True)
    
    factory_id: int = Field(..., description="工厂ID")
    json_field: str = Field(..., description="JSON字段名")
    search_criteria: Dict[str, Any] = Field(..., description="搜索条件")
    skip: int = Field(0, ge=0, description="跳过条数")
    limit: int = Field(100, ge=1, le=1000, description="返回条数")


class CreateTemplateTagOperationDTO(BaseModel):
    """DTO for tag operations."""
    model_config = ConfigDict(from_attributes=True)
    
    template_id: int = Field(..., description="模板ID")
    tag: str = Field(..., max_length=50, description="标签")
    updated_by_user_id: Optional[int] = Field(None, description="更新者用户ID")


class CreateTemplateAccessControlDTO(BaseModel):
    """DTO for access control operations."""
    model_config = ConfigDict(from_attributes=True)
    
    template_id: int = Field(..., description="模板ID")
    access_level: str = Field(..., description="访问级别")
    is_public: Optional[bool] = Field(None, description="是否公开")
    updated_by_user_id: Optional[int] = Field(None, description="更新者用户ID")


class CreateTemplateUsageTrackingDTO(BaseModel):
    """DTO for tracking template usage."""
    model_config = ConfigDict(from_attributes=True)
    
    template_id: int = Field(..., description="模板ID")
    used_by_user_id: Optional[int] = Field(None, description="使用者用户ID")
    usage_context: Optional[Dict[str, Any]] = Field(None, description="使用上下文")


class CreateTemplateValidationDTO(BaseModel):
    """DTO for template validation."""
    model_config = ConfigDict(from_attributes=True)
    
    template_id: int = Field(..., description="模板ID")
    validation_context: Optional[Dict[str, Any]] = Field(None, description="验证上下文")


class CreateTemplateCleanupDTO(BaseModel):
    """DTO for template cleanup operations."""
    model_config = ConfigDict(from_attributes=True)
    
    factory_id: int = Field(..., description="工厂ID")
    older_than_days: Optional[int] = Field(None, ge=1, description="早于天数")
    unused_for_days: Optional[int] = Field(None, ge=1, description="未使用天数")
    exclude_system: bool = Field(True, description="排除系统模板")


class CreateTemplateImportDTO(BaseModel):
    """DTO for importing templates."""
    model_config = ConfigDict(from_attributes=True)
    
    factory_id: int = Field(..., description="工厂ID")
    templates_data: List[Dict[str, Any]] = Field(..., description="模板数据列表")
    import_mode: str = Field("create", description="导入模式: create, update, upsert")
    created_by_user_id: int = Field(..., description="导入者用户ID")


class CreateTemplateExportDTO(BaseModel):
    """DTO for exporting templates."""
    model_config = ConfigDict(from_attributes=True)
    
    factory_id: int = Field(..., description="工厂ID")
    template_ids: Optional[List[int]] = Field(None, description="要导出的模板ID列表")
    export_format: str = Field("json", description="导出格式")
    include_metadata: bool = Field(True, description="是否包含元数据")
    include_usage_stats: bool = Field(False, description="是否包含使用统计")