from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime
from decimal import Decimal
from .order_craft_dto import OrderCraftCreateDTO, OrderCraftResponseDTO
from .order_part_dto import OrderPartResponseDTO


class OrderLineBaseDTO(BaseModel):
    """Base order line DTO."""
    size: str = Field(..., min_length=1, max_length=20, description="尺码")
    amount: int = Field(..., ge=0, description="数量")
    notes: Optional[str] = Field(None, max_length=500, description="备注")


class OrderLineCreateDTO(OrderLineBaseDTO):
    """DTO for creating a new order line."""
    pass


class OrderLineUpdateDTO(BaseModel):
    """DTO for updating an order line."""
    amount: Optional[int] = Field(None, ge=0, description="数量")
    produced_amount: Optional[int] = Field(None, ge=0, description="已生产数量")
    completed_amount: Optional[int] = Field(None, ge=0, description="已完成数量")
    notes: Optional[str] = Field(None, max_length=500, description="备注")


class OrderLineResponseDTO(OrderLineBaseDTO):
    """DTO for order line response."""
    id: int = Field(..., description="订单行ID")
    order_no: str = Field(..., description="订单号")
    order_line_no: str = Field(..., description="订单行号")
    amount: int = Field(..., description="数量")
    produced_amount: Optional[int] = Field(None, description="已生产数量")
    completed_amount: Optional[int] = Field(None,description="已完成数量")
    completion_percentage: Optional[float] = Field(None, description="完成百分比")
    production_percentage: Optional[float] = Field(None, description="生产百分比")
    remaining_amount: Optional[int] = Field(None, description="剩余数量")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class OrderBaseDTO(BaseModel):
    """Base order DTO."""
    skc_no: str = Field(..., min_length=1, max_length=100, description="款号")
    external_skc_no: Optional[str] = Field(None, max_length=100, description="外部款号")
    order_no: str = Field(..., min_length=1, max_length=100, description="订单号")
    external_order_no: Optional[str] = Field(None, max_length=100, description="外部订单号")
    external_order_no2: Optional[str] = Field(None, max_length=100, description="外部订单号2")
    cost: Optional[Decimal] = Field(None, ge=0, description="成本")
    price: Optional[Decimal] = Field(None, ge=0, description="价格")
    expect_finished_at: Optional[datetime] = Field(None, description="预期完成时间")
    owner_user_id: Optional[int] = Field(None, description="负责人用户ID")
    description: Optional[str] = Field(None, max_length=1000, description="订单描述")
    notes: Optional[str] = Field(None, max_length=1000, description="备注")
    part_types: Optional[List[str]] = Field(None, description="订单所需的部位类型列表")


class OrderCreateDTO(OrderBaseDTO):
    """DTO for creating a new order."""
    order_lines: List[OrderLineCreateDTO] = Field(..., min_items=1, description="订单行列表")
    order_crafts: Optional[List[OrderCraftCreateDTO]] = Field(None, description="订单工艺配置列表")


class OrderUpdateDTO(BaseModel):
    """DTO for updating an order."""
    skc_no: Optional[str] = Field(None, min_length=1, max_length=100, description="款号")
    external_skc_no: Optional[str] = Field(None, max_length=100, description="外部款号")
    external_order_no: Optional[str] = Field(None, max_length=100, description="外部订单号")
    external_order_no2: Optional[str] = Field(None, max_length=100, description="外部订单号2")
    cost: Optional[Decimal] = Field(None, ge=0, description="成本")
    price: Optional[Decimal] = Field(None, ge=0, description="价格")
    expect_finished_at: Optional[datetime] = Field(None, description="预期完成时间")
    owner_user_id: Optional[int] = Field(None, description="负责人用户ID")
    description: Optional[str] = Field(None, max_length=1000, description="订单描述")
    notes: Optional[str] = Field(None, max_length=1000, description="备注")
    part_types: Optional[List[str]] = Field(None, description="订单所需的部位类型列表")


class OrderResponseDTO(OrderBaseDTO):
    """DTO for order response."""
    id: int = Field(..., description="订单ID")
    total_amount: int = Field(..., description="总数量")
    status: str = Field(..., description="订单状态")
    current_craft: Optional[str] = Field(None, description="当前工艺")
    current_craft_route: Optional[int] = Field(None, description="当前工艺路线")
    completion_percentage: Optional[float] = Field(None, description="完成百分比")
    created_at: datetime = Field(..., description="创建时间")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    finished_at: Optional[datetime] = Field(None, description="完成时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class OrderDetailResponseDTO(OrderResponseDTO):
    """DTO for detailed order response with lines, crafts, and parts."""
    order_lines: List[OrderLineResponseDTO] = Field(..., description="订单行列表")
    order_crafts: Optional[List[OrderCraftResponseDTO]] = Field(None, description="订单工艺配置列表")
    order_parts: Optional[List[OrderPartResponseDTO]] = Field(None, description="订单部位列表")

# Backward compatibility alias
OrderWithLinesDTO = OrderDetailResponseDTO


class OrderListDTO(BaseModel):
    """DTO for order list response."""
    orders: List[OrderResponseDTO] = Field(..., description="订单列表")
    total: int = Field(..., description="总数量")


class OrderSearchDTO(BaseModel):
    """DTO for order search criteria."""
    search_term: Optional[str] = Field(None, description="搜索关键词 (订单号、款号等)")
    status: Optional[str] = Field(None, description="订单状态")
    owner_user_id: Optional[int] = Field(None, description="负责人用户ID")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")
    date_field: Optional[str] = Field("created_at", description="日期字段 (created_at, expect_finished_at, finished_at)")
    current_craft: Optional[str] = Field(None, description="当前工艺")


class OrderStatusUpdateDTO(BaseModel):
    """DTO for updating order status."""
    status: str = Field(..., description="新状态")
    notes: Optional[str] = Field(None, description="状态变更备注")


class OrderCraftProgressDTO(BaseModel):
    """DTO for updating order craft progress."""
    craft_code: str = Field(..., description="工艺代码")
    craft_route_id: Optional[int] = Field(None, description="工艺路线ID")
    notes: Optional[str] = Field(None, description="进度备注")


class OrderLineProductionUpdateDTO(BaseModel):
    """DTO for updating order line production."""
    order_line_id: int = Field(..., description="订单行ID")
    produced_amount: Optional[int] = Field(None, ge=0, description="已生产数量")
    completed_amount: Optional[int] = Field(None, ge=0, description="已完成数量")
    notes: Optional[str] = Field(None, max_length=500, description="备注")

class OrderProductionUpdateDTO(BaseModel):
    """DTO for updating order line production."""
    order_line_updates: List[OrderLineProductionUpdateDTO] = Field(..., description="订单行更新列表")


class BulkOrderLineCreateDTO(BaseModel):
    """DTO for creating multiple order lines."""
    order_no: str = Field(..., description="订单号")
    order_lines: List[OrderLineCreateDTO] = Field(..., min_items=1, description="订单行列表")


class OrderOperationResultDTO(BaseModel):
    """DTO for order operation results."""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="结果消息")
    order_id: Optional[int] = Field(None, description="订单ID")
    order_no: Optional[str] = Field(None, description="订单号")
    details: Optional[dict] = Field(None, description="详细信息")


class OrderAmountUpdateDTO(BaseModel):
    """DTO for updating order total amount."""
    total_amount: int = Field(..., ge=0, description="新的总数量")
    notes: Optional[str] = Field(None, description="变更备注")


class OrderStatisticsDTO(BaseModel):
    """DTO for order statistics."""
    total_orders: int = Field(..., description="总订单数")
    active_orders: int = Field(..., description="活跃订单数")
    completed_orders: int = Field(..., description="已完成订单数")
    overdue_orders: int = Field(..., description="逾期订单数")
    status_breakdown: dict = Field(..., description="状态分解")
    total_amount: int = Field(..., description="总数量")
    total_completed_amount: int = Field(..., description="总完成数量")
    completion_rate: float = Field(..., description="完成率")


class OrderLineStatisticsDTO(BaseModel):
    """DTO for order line statistics."""
    total_lines: int = Field(..., description="总订单行数")
    completed_lines: int = Field(..., description="已完成订单行数")
    in_progress_lines: int = Field(..., description="进行中订单行数")
    pending_lines: int = Field(..., description="待处理订单行数")
    total_amount: int = Field(..., description="总数量")
    total_produced: int = Field(..., description="总生产数量")
    total_completed: int = Field(..., description="总完成数量")
    completion_percentage: float = Field(..., description="完成百分比")
    production_percentage: float = Field(..., description="生产百分比")


class OrderSummaryDTO(BaseModel):
    """DTO for order summary information."""
    id: int = Field(..., description="订单ID")
    order_no: str = Field(..., description="订单号")
    skc_no: str = Field(..., description="款号")
    status: str = Field(..., description="订单状态")
    total_amount: int = Field(..., description="总数量")
    completed_amount: int = Field(..., description="已完成数量")
    completion_percentage: float = Field(..., description="完成百分比")
    owner_name: Optional[str] = Field(None, description="负责人姓名")
    current_craft: Optional[str] = Field(None, description="当前工艺")
    expect_finished_at: Optional[datetime] = Field(None, description="预期完成时间")
    created_at: datetime = Field(..., description="创建时间")
    
    class Config:
        from_attributes = True


class OrderDashboardDTO(BaseModel):
    """DTO for order dashboard data."""
    order_statistics: OrderStatisticsDTO = Field(..., description="订单统计")
    recent_orders: List[OrderSummaryDTO] = Field(..., description="最近订单")
    overdue_orders: List[OrderSummaryDTO] = Field(..., description="逾期订单")
    orders_by_craft: dict = Field(..., description="按工艺分组的订单")
    production_summary: OrderLineStatisticsDTO = Field(..., description="生产汇总")


class OrderPartTypesUpdateDTO(BaseModel):
    """DTO for updating order part types."""
    part_types: List[str] = Field(..., description="订单所需的部位类型列表")
    
    
class OrderPartTypesValidationDTO(BaseModel):
    """DTO for order part types validation results."""
    valid: bool = Field(..., description="验证是否通过")
    missing_parts: List[str] = Field(..., description="缺失的部位类型")
    extra_parts: List[str] = Field(..., description="多余的部位类型")
    order_no: str = Field(..., description="订单号")