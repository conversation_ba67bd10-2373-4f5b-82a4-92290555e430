from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from datetime import datetime
from src.domain.entities.create_template import CreateTemplate, TemplateStatus


class CreateTemplateRepositoryInterface(ABC):
    """Repository interface for CreateTemplate operations."""

    @abstractmethod
    async def create(self, template: CreateTemplate) -> CreateTemplate:
        """Create a new template."""
        pass

    @abstractmethod
    async def get_by_id(self, template_id: int) -> Optional[CreateTemplate]:
        """Get template by ID."""
        pass

    @abstractmethod
    async def get_by_code(self, template_code: str, factory_id: int) -> Optional[CreateTemplate]:
        """Get template by code and factory."""
        pass

    @abstractmethod
    async def get_by_name(self, template_name: str, factory_id: int) -> Optional[CreateTemplate]:
        """Get template by name and factory."""
        pass

    @abstractmethod
    async def get_by_part_type(self, part_type: str, factory_id: int, status: Optional[TemplateStatus] = None) -> List[CreateTemplate]:
        """Get templates by part type."""
        pass

    @abstractmethod
    async def get_by_category(self, category: str, factory_id: int) -> List[CreateTemplate]:
        """Get templates by category."""
        pass

    @abstractmethod
    async def get_by_status(self, status: TemplateStatus, factory_id: int, skip: int = 0, limit: int = 100) -> List[CreateTemplate]:
        """Get templates by status."""
        pass

    @abstractmethod
    async def get_by_creator(self, creator_user_id: int, factory_id: int) -> List[CreateTemplate]:
        """Get templates created by a specific user."""
        pass

    @abstractmethod
    async def get_by_tags(self, tags: List[str], factory_id: int, match_all: bool = False) -> List[CreateTemplate]:
        """Get templates by tags."""
        pass

    @abstractmethod
    async def search_templates(
        self,
        factory_id: int,
        part_type: Optional[str] = None,
        template_name: Optional[str] = None,
        template_code: Optional[str] = None,
        search_params: Optional[Dict[str, Any]] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[CreateTemplate]:
        """Search templates with flexible parameters."""
        pass

    @abstractmethod
    async def get_public_templates(self, factory_id: int, part_type: Optional[str] = None) -> List[CreateTemplate]:
        """Get all public templates."""
        pass

    @abstractmethod
    async def get_system_templates(self, factory_id: int) -> List[CreateTemplate]:
        """Get system templates."""
        pass

    @abstractmethod
    async def get_accessible_templates(
        self, 
        factory_id: int, 
        user_access_level: str, 
        part_type: Optional[str] = None
    ) -> List[CreateTemplate]:
        """Get templates accessible to user based on access level."""
        pass

    @abstractmethod
    async def get_templates_by_parent(self, parent_template_id: int) -> List[CreateTemplate]:
        """Get child templates of a parent template."""
        pass

    @abstractmethod
    async def get_most_used_templates(
        self, 
        factory_id: int, 
        part_type: Optional[str] = None, 
        limit: int = 10
    ) -> List[CreateTemplate]:
        """Get most frequently used templates."""
        pass

    @abstractmethod
    async def get_recently_used_templates(
        self, 
        factory_id: int, 
        user_id: Optional[int] = None, 
        limit: int = 10
    ) -> List[CreateTemplate]:
        """Get recently used templates."""
        pass

    @abstractmethod
    async def get_recently_created_templates(self, factory_id: int, limit: int = 10) -> List[CreateTemplate]:
        """Get recently created templates."""
        pass

    @abstractmethod
    async def get_templates_by_date_range(
        self,
        factory_id: int,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        date_field: str = "created_at"
    ) -> List[CreateTemplate]:
        """Get templates within a date range."""
        pass

    @abstractmethod
    async def update(self, template: CreateTemplate) -> CreateTemplate:
        """Update an existing template."""
        pass

    @abstractmethod
    async def delete(self, template_id: int) -> bool:
        """Delete a template."""
        pass

    @abstractmethod
    async def bulk_create(self, templates: List[CreateTemplate]) -> List[CreateTemplate]:
        """Create multiple templates."""
        pass

    @abstractmethod
    async def bulk_update_status(
        self, 
        template_ids: List[int], 
        status: TemplateStatus
    ) -> List[CreateTemplate]:
        """Bulk update status for multiple templates."""
        pass

    @abstractmethod
    async def duplicate_template(
        self, 
        template_id: int, 
        new_name: str, 
        new_code: str, 
        creator_user_id: int
    ) -> CreateTemplate:
        """Duplicate an existing template."""
        pass

    @abstractmethod
    async def get_template_statistics(self, factory_id: int) -> Dict[str, Any]:
        """Get template statistics."""
        pass

    @abstractmethod
    async def get_usage_statistics(
        self, 
        factory_id: int, 
        start_date: Optional[datetime] = None, 
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get template usage statistics."""
        pass

    @abstractmethod
    async def count_by_status(self, factory_id: int) -> Dict[str, int]:
        """Count templates by status."""
        pass

    @abstractmethod
    async def count_by_part_type(self, factory_id: int) -> Dict[str, int]:
        """Count templates by part type."""
        pass

    @abstractmethod
    async def count_by_category(self, factory_id: int) -> Dict[str, int]:
        """Count templates by category."""
        pass

    @abstractmethod
    async def get_template_versions(self, template_code: str, factory_id: int) -> List[CreateTemplate]:
        """Get all versions of a template by code."""
        pass

    @abstractmethod
    async def get_latest_version(self, template_code: str, factory_id: int) -> Optional[CreateTemplate]:
        """Get the latest version of a template."""
        pass

    @abstractmethod
    async def search_by_json_field(
        self,
        factory_id: int,
        json_field: str,
        search_criteria: Dict[str, Any],
        skip: int = 0,
        limit: int = 100
    ) -> List[CreateTemplate]:
        """Search templates by JSON field content."""
        pass

    @abstractmethod
    async def get_templates_with_validation_rules(self, factory_id: int) -> List[CreateTemplate]:
        """Get templates that have validation rules defined."""
        pass

    @abstractmethod
    async def archive_old_templates(
        self, 
        factory_id: int, 
        older_than_days: int, 
        exclude_system: bool = True
    ) -> int:
        """Archive templates older than specified days."""
        pass

    @abstractmethod
    async def cleanup_unused_templates(
        self, 
        factory_id: int, 
        unused_for_days: int
    ) -> int:
        """Clean up templates that haven't been used for specified days."""
        pass

    @abstractmethod
    async def get_by_ids(self, template_ids: List[int]) -> List[CreateTemplate]:
        """Get multiple templates by IDs."""
        pass