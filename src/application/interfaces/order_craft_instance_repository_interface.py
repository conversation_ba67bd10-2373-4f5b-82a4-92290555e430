from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from datetime import datetime
from src.domain.entities.order_craft_instance import OrderCraftInstance, OrderCraftInstanceStatus


class OrderCraftInstanceRepositoryInterface(ABC):
    """Repository interface for OrderCraftInstance operations."""

    @abstractmethod
    async def create(self, order_craft_instance: OrderCraftInstance) -> OrderCraftInstance:
        """Create a new order craft instance."""
        pass

    @abstractmethod
    async def get_by_id(self, instance_id: int) -> Optional[OrderCraftInstance]:
        """Get order craft instance by ID."""
        pass

    @abstractmethod
    async def get_by_order_and_part_and_craft(
        self, 
        order_no: str, 
        order_part_no: str, 
        craft_code: str, 
        factory_id: int
    ) -> Optional[OrderCraftInstance]:
        """Get order craft instance by order, part, and craft."""
        pass

    @abstractmethod
    async def get_by_order_no(self, order_no: str, factory_id: int) -> List[OrderCraftInstance]:
        """Get all order craft instances for a specific order."""
        pass

    @abstractmethod
    async def get_by_order_part_no(self, order_part_no: str, factory_id: int) -> List[OrderCraftInstance]:
        """Get all order craft instances for a specific order part."""
        pass

    @abstractmethod
    async def get_by_craft_code(self, craft_code: str, factory_id: int) -> List[OrderCraftInstance]:
        """Get all order craft instances for a specific craft."""
        pass

    @abstractmethod
    async def get_by_status(
        self, 
        status: OrderCraftInstanceStatus, 
        factory_id: int, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[OrderCraftInstance]:
        """Get order craft instances by status."""
        pass

    @abstractmethod
    async def get_by_assigned_user(
        self, 
        user_id: int, 
        factory_id: int, 
        status: Optional[OrderCraftInstanceStatus] = None
    ) -> List[OrderCraftInstance]:
        """Get order craft instances assigned to a specific user."""
        pass

    @abstractmethod
    async def get_by_supervisor(
        self, 
        supervisor_id: int, 
        factory_id: int, 
        status: Optional[OrderCraftInstanceStatus] = None
    ) -> List[OrderCraftInstance]:
        """Get order craft instances supervised by a specific user."""
        pass

    @abstractmethod
    async def get_by_part_type(
        self, 
        part_type: str, 
        factory_id: int, 
        status: Optional[OrderCraftInstanceStatus] = None
    ) -> List[OrderCraftInstance]:
        """Get order craft instances by part type."""
        pass

    @abstractmethod
    async def get_by_date_range(
        self,
        factory_id: int,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        date_field: str = "created_at"
    ) -> List[OrderCraftInstance]:
        """Get order craft instances within a date range."""
        pass

    @abstractmethod
    async def get_overdue_instances(self, factory_id: int) -> List[OrderCraftInstance]:
        """Get all overdue order craft instances."""
        pass

    @abstractmethod
    async def get_in_progress_instances(self, factory_id: int) -> List[OrderCraftInstance]:
        """Get all in-progress order craft instances."""
        pass

    @abstractmethod
    async def get_pending_instances(self, factory_id: int) -> List[OrderCraftInstance]:
        """Get all pending order craft instances."""
        pass

    @abstractmethod
    async def search_instances(
        self,
        factory_id: int,
        search_params: Dict[str, Any],
        skip: int = 0,
        limit: int = 100
    ) -> List[OrderCraftInstance]:
        """Search order craft instances with flexible parameters."""
        pass

    @abstractmethod
    async def update(self, order_craft_instance: OrderCraftInstance) -> OrderCraftInstance:
        """Update an existing order craft instance."""
        pass

    @abstractmethod
    async def delete(self, instance_id: int) -> bool:
        """Delete an order craft instance."""
        pass

    @abstractmethod
    async def bulk_create(self, instances: List[OrderCraftInstance]) -> List[OrderCraftInstance]:
        """Create multiple order craft instances."""
        pass

    @abstractmethod
    async def bulk_update_status(
        self, 
        instance_ids: List[int], 
        status: OrderCraftInstanceStatus,
        notes: Optional[str] = None
    ) -> List[OrderCraftInstance]:
        """Bulk update status for multiple instances."""
        pass

    @abstractmethod
    async def get_statistics_by_order(self, order_no: str, factory_id: int) -> Dict[str, Any]:
        """Get statistics for order craft instances by order."""
        pass

    @abstractmethod
    async def get_statistics_by_part_type(self, factory_id: int) -> Dict[str, Any]:
        """Get statistics for order craft instances by part type."""
        pass

    @abstractmethod
    async def get_statistics_by_craft(self, factory_id: int) -> Dict[str, Any]:
        """Get statistics for order craft instances by craft."""
        pass

    @abstractmethod
    async def get_statistics_by_user(self, user_id: int, factory_id: int) -> Dict[str, Any]:
        """Get statistics for order craft instances by user."""
        pass

    @abstractmethod
    async def get_workflow_status(self, order_no: str, order_part_no: str, factory_id: int) -> List[OrderCraftInstance]:
        """Get workflow status for a specific order part (all crafts for the part)."""
        pass

    @abstractmethod
    async def get_next_craft_instances(self, order_no: str, order_part_no: str, factory_id: int) -> List[OrderCraftInstance]:
        """Get next available craft instances for a specific order part."""
        pass

    @abstractmethod
    async def count_by_status(self, factory_id: int) -> Dict[str, int]:
        """Count order craft instances by status."""
        pass

    @abstractmethod
    async def get_performance_metrics(
        self, 
        factory_id: int, 
        start_date: Optional[datetime] = None, 
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get performance metrics for order craft instances."""
        pass