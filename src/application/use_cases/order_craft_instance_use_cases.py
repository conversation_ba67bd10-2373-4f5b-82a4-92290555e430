from typing import List, Optional, Dict, Any
from datetime import datetime
from src.application.interfaces.order_craft_instance_repository_interface import OrderCraftInstanceRepositoryInterface
from src.application.interfaces.order_repository_interface import OrderRepositoryInterface
from src.application.interfaces.order_part_repository_interface import OrderPartRepositoryInterface
from src.application.interfaces.order_craft_repository_interface import OrderCraftRepositoryInterface
from src.application.interfaces.user_repository_interface import UserRepositoryInterface
from src.application.dto.order_craft_instance_dto import (
    OrderCraftInstanceCreateDTO, OrderCraftInstanceUpdateDTO, OrderCraftInstanceResponseDTO,
    OrderCraftInstanceDetailDTO, OrderCraftInstanceListDTO, OrderCraftInstanceSearchDTO,
    OrderCraftInstanceStatusUpdateDTO, OrderCraftInstanceAssignmentDTO, OrderCraftInstanceProgressDTO,
    OrderCraftInstanceQualityDTO, OrderCraftInstanceBulkCreateDTO, OrderCraftInstanceBulkUpdateDTO,
    OrderCraftInstanceStatisticsDTO, OrderCraftInstanceWorkflowDTO, OrderCraftInstancePerformanceDTO,
    OrderCraftInstanceOperationResultDTO, OrderCraftInstanceDashboardDTO, OrderCraftInstanceStatusDTO
)
from src.domain.entities.order_craft_instance import OrderCraftInstance, OrderCraftInstanceStatus
from src.domain.entities.order_part import PartType


class OrderCraftInstanceUseCases:
    """Use cases for order craft instance management operations."""

    def __init__(
        self,
        order_craft_instance_repository: OrderCraftInstanceRepositoryInterface,
        order_repository: OrderRepositoryInterface,
        order_part_repository: OrderPartRepositoryInterface,
        order_craft_repository: OrderCraftRepositoryInterface,
        user_repository: UserRepositoryInterface
    ):
        self.order_craft_instance_repository = order_craft_instance_repository
        self.order_repository = order_repository
        self.order_part_repository = order_part_repository
        self.order_craft_repository = order_craft_repository
        self.user_repository = user_repository

    # Basic CRUD operations
    async def create_craft_instance(
        self, 
        instance_data: OrderCraftInstanceCreateDTO, 
        factory_id: int
    ) -> OrderCraftInstanceResponseDTO:
        """Create a new order craft instance."""
        # Validate order exists
        order = await self.order_repository.get_by_id(instance_data.order_id)
        if not order:
            raise ValueError(f"Order with ID {instance_data.order_id} not found")

        # Validate order part exists
        order_part = await self.order_part_repository.get_by_id(instance_data.order_part_id)
        if not order_part:
            raise ValueError(f"Order part with ID {instance_data.order_part_id} not found")

        # Validate order craft exists
        order_craft = await self.order_craft_repository.get_by_id(instance_data.order_craft_id)
        if not order_craft:
            raise ValueError(f"Order craft with ID {instance_data.order_craft_id} not found")

        # Check if instance already exists
        existing = await self.order_craft_instance_repository.get_by_order_and_part_and_craft(
            instance_data.order_no, instance_data.order_part_no, instance_data.craft_code, factory_id
        )
        if existing:
            raise ValueError(f"Order craft instance already exists for {instance_data.order_no}/{instance_data.order_part_no}/{instance_data.craft_code}")

        # Validate assigned user if specified
        if instance_data.assigned_user_id:
            user = await self.user_repository.get_by_id(instance_data.assigned_user_id)
            if not user:
                raise ValueError(f"Assigned user with ID {instance_data.assigned_user_id} not found")

        # Validate supervisor if specified
        if instance_data.supervisor_user_id:
            supervisor = await self.user_repository.get_by_id(instance_data.supervisor_user_id)
            if not supervisor:
                raise ValueError(f"Supervisor with ID {instance_data.supervisor_user_id} not found")

        # Create the instance
        craft_instance = OrderCraftInstance(
            factory_id=factory_id,
            order_no=instance_data.order_no,
            order_part_no=instance_data.order_part_no,
            part_type=instance_data.part_type,
            craft_code=instance_data.craft_code,
            order_id=instance_data.order_id,
            order_part_id=instance_data.order_part_id,
            order_craft_id=instance_data.order_craft_id,
            sequence_order=instance_data.sequence_order,
            is_required=instance_data.is_required,
            is_active=instance_data.is_active,
            planned_start_date=instance_data.planned_start_date,
            planned_end_date=instance_data.planned_end_date,
            estimated_duration_minutes=instance_data.estimated_duration_minutes,
            assigned_user_id=instance_data.assigned_user_id,
            supervisor_user_id=instance_data.supervisor_user_id,
            notes=instance_data.notes
        )

        created_instance = await self.order_craft_instance_repository.create(craft_instance)
        return await self._build_instance_response_dto(created_instance)

    async def get_craft_instance_by_id(self, instance_id: int) -> Optional[OrderCraftInstanceDetailDTO]:
        """Get order craft instance by ID with detailed information."""
        instance = await self.order_craft_instance_repository.get_by_id(instance_id)
        if instance:
            return await self._build_instance_detail_dto(instance)
        return None

    async def get_craft_instance_by_key(
        self, 
        order_no: str, 
        order_part_no: str, 
        craft_code: str, 
        factory_id: int
    ) -> Optional[OrderCraftInstanceDetailDTO]:
        """Get order craft instance by unique key."""
        instance = await self.order_craft_instance_repository.get_by_order_and_part_and_craft(
            order_no, order_part_no, craft_code, factory_id
        )
        if instance:
            return await self._build_instance_detail_dto(instance)
        return None

    async def update_craft_instance(
        self, 
        instance_id: int, 
        update_data: OrderCraftInstanceUpdateDTO
    ) -> Optional[OrderCraftInstanceResponseDTO]:
        """Update an existing order craft instance."""
        instance = await self.order_craft_instance_repository.get_by_id(instance_id)
        if not instance:
            return None

        # Validate assigned user if specified
        if update_data.assigned_user_id:
            user = await self.user_repository.get_by_id(update_data.assigned_user_id)
            if not user:
                raise ValueError(f"Assigned user with ID {update_data.assigned_user_id} not found")

        # Validate supervisor if specified
        if update_data.supervisor_user_id:
            supervisor = await self.user_repository.get_by_id(update_data.supervisor_user_id)
            if not supervisor:
                raise ValueError(f"Supervisor with ID {update_data.supervisor_user_id} not found")

        # Update fields if provided
        if update_data.status is not None:
            instance.status = OrderCraftInstanceStatus(update_data.status.value)
        if update_data.progress_percentage is not None:
            instance.update_progress(update_data.progress_percentage)
        if update_data.sequence_order is not None:
            instance.sequence_order = update_data.sequence_order
        if update_data.is_required is not None:
            instance.is_required = update_data.is_required
        if update_data.is_active is not None:
            instance.is_active = update_data.is_active
        if update_data.planned_start_date is not None:
            instance.planned_start_date = update_data.planned_start_date
        if update_data.planned_end_date is not None:
            instance.planned_end_date = update_data.planned_end_date
        if update_data.estimated_duration_minutes is not None:
            instance.estimated_duration_minutes = update_data.estimated_duration_minutes
        if update_data.assigned_user_id is not None:
            instance.assigned_user_id = update_data.assigned_user_id
        if update_data.supervisor_user_id is not None:
            instance.supervisor_user_id = update_data.supervisor_user_id
        if update_data.quality_score is not None:
            instance.set_quality_score(update_data.quality_score)
        if update_data.notes is not None:
            instance.notes = update_data.notes
        if update_data.completion_notes is not None:
            instance.completion_notes = update_data.completion_notes

        updated_instance = await self.order_craft_instance_repository.update(instance)
        return await self._build_instance_response_dto(updated_instance)

    async def delete_craft_instance(self, instance_id: int) -> OrderCraftInstanceOperationResultDTO:
        """Delete an order craft instance."""
        instance = await self.order_craft_instance_repository.get_by_id(instance_id)
        if not instance:
            return OrderCraftInstanceOperationResultDTO(
                success=False,
                message="Order craft instance not found",
                instance_id=instance_id
            )

        success = await self.order_craft_instance_repository.delete(instance_id)
        return OrderCraftInstanceOperationResultDTO(
            success=success,
            message="Order craft instance deleted successfully" if success else "Failed to delete order craft instance",
            instance_id=instance_id
        )

    # Status management operations
    async def start_craft_instance(self, instance_id: int) -> Optional[OrderCraftInstanceResponseDTO]:
        """Start a craft instance."""
        instance = await self.order_craft_instance_repository.get_by_id(instance_id)
        if not instance:
            return None

        if not instance.can_start():
            raise ValueError(f"Craft instance cannot be started. Current status: {instance.status.value}")

        instance.start_instance()
        updated_instance = await self.order_craft_instance_repository.update(instance)
        return await self._build_instance_response_dto(updated_instance)

    async def complete_craft_instance(
        self, 
        instance_id: int, 
        completion_notes: Optional[str] = None
    ) -> Optional[OrderCraftInstanceResponseDTO]:
        """Complete a craft instance."""
        instance = await self.order_craft_instance_repository.get_by_id(instance_id)
        if not instance:
            return None

        if not instance.can_complete():
            raise ValueError(f"Craft instance cannot be completed. Current status: {instance.status.value}")

        instance.complete_instance(completion_notes)
        updated_instance = await self.order_craft_instance_repository.update(instance)
        return await self._build_instance_response_dto(updated_instance)

    async def update_craft_instance_status(
        self, 
        instance_id: int, 
        status_update: OrderCraftInstanceStatusUpdateDTO
    ) -> Optional[OrderCraftInstanceResponseDTO]:
        """Update craft instance status."""
        instance = await self.order_craft_instance_repository.get_by_id(instance_id)
        if not instance:
            return None

        new_status = OrderCraftInstanceStatus(status_update.status.value)
        
        # Apply status-specific logic
        if new_status == OrderCraftInstanceStatus.IN_PROGRESS:
            instance.start_instance()
        elif new_status == OrderCraftInstanceStatus.COMPLETED:
            instance.complete_instance(status_update.completion_notes)
        elif new_status == OrderCraftInstanceStatus.ON_HOLD:
            instance.hold_instance(status_update.notes)
        elif new_status == OrderCraftInstanceStatus.SKIPPED:
            instance.skip_instance(status_update.notes)
        elif new_status == OrderCraftInstanceStatus.FAILED:
            instance.fail_instance(status_update.notes)
        elif new_status == OrderCraftInstanceStatus.PENDING:
            instance.reset_instance()

        if status_update.notes and new_status not in [OrderCraftInstanceStatus.ON_HOLD, OrderCraftInstanceStatus.SKIPPED, OrderCraftInstanceStatus.FAILED]:
            instance.notes = status_update.notes

        updated_instance = await self.order_craft_instance_repository.update(instance)
        return await self._build_instance_response_dto(updated_instance)

    # Assignment operations
    async def assign_craft_instance(
        self, 
        instance_id: int, 
        assignment: OrderCraftInstanceAssignmentDTO
    ) -> Optional[OrderCraftInstanceResponseDTO]:
        """Assign a craft instance to users."""
        instance = await self.order_craft_instance_repository.get_by_id(instance_id)
        if not instance:
            return None

        if assignment.assigned_user_id:
            user = await self.user_repository.get_by_id(assignment.assigned_user_id)
            if not user:
                raise ValueError(f"Assigned user with ID {assignment.assigned_user_id} not found")
            instance.assign_to_user(assignment.assigned_user_id)

        if assignment.supervisor_user_id:
            supervisor = await self.user_repository.get_by_id(assignment.supervisor_user_id)
            if not supervisor:
                raise ValueError(f"Supervisor with ID {assignment.supervisor_user_id} not found")
            instance.set_supervisor(assignment.supervisor_user_id)

        if assignment.notes:
            instance.notes = f"{instance.notes or ''}\nAssignment: {assignment.notes}".strip()

        updated_instance = await self.order_craft_instance_repository.update(instance)
        return await self._build_instance_response_dto(updated_instance)

    # Progress and quality management
    async def update_craft_instance_progress(
        self, 
        instance_id: int, 
        progress: OrderCraftInstanceProgressDTO
    ) -> Optional[OrderCraftInstanceResponseDTO]:
        """Update craft instance progress."""
        instance = await self.order_craft_instance_repository.get_by_id(instance_id)
        if not instance:
            return None

        instance.update_progress(progress.progress_percentage)
        
        if progress.quality_score is not None:
            instance.set_quality_score(progress.quality_score)
        
        if progress.notes:
            instance.notes = f"{instance.notes or ''}\nProgress Update: {progress.notes}".strip()

        updated_instance = await self.order_craft_instance_repository.update(instance)
        return await self._build_instance_response_dto(updated_instance)

    async def update_craft_instance_quality(
        self, 
        instance_id: int, 
        quality: OrderCraftInstanceQualityDTO
    ) -> Optional[OrderCraftInstanceResponseDTO]:
        """Update craft instance quality metrics."""
        instance = await self.order_craft_instance_repository.get_by_id(instance_id)
        if not instance:
            return None

        if quality.quality_score is not None:
            instance.set_quality_score(quality.quality_score)
        
        if quality.rework_count is not None:
            # Add the difference in rework count
            additional_rework = quality.rework_count - instance.rework_count
            if additional_rework > 0:
                for _ in range(additional_rework):
                    instance.add_rework(quality.notes)
        
        if quality.defect_count is not None:
            # Add the difference in defect count
            additional_defects = quality.defect_count - instance.defect_count
            if additional_defects > 0:
                instance.add_defect(additional_defects, quality.notes)

        if quality.notes:
            instance.notes = f"{instance.notes or ''}\nQuality Update: {quality.notes}".strip()

        updated_instance = await self.order_craft_instance_repository.update(instance)
        return await self._build_instance_response_dto(updated_instance)

    # Bulk operations
    async def bulk_create_craft_instances(
        self, 
        bulk_data: OrderCraftInstanceBulkCreateDTO, 
        factory_id: int
    ) -> OrderCraftInstanceOperationResultDTO:
        """Bulk create order craft instances."""
        try:
            instances = []
            for instance_data in bulk_data.instances:
                craft_instance = OrderCraftInstance(
                    factory_id=factory_id,
                    order_no=bulk_data.order_no,
                    order_part_no=bulk_data.order_part_no,
                    part_type=instance_data.part_type,
                    craft_code=instance_data.craft_code,
                    order_id=instance_data.order_id,
                    order_part_id=instance_data.order_part_id,
                    order_craft_id=instance_data.order_craft_id,
                    sequence_order=instance_data.sequence_order,
                    is_required=instance_data.is_required,
                    is_active=instance_data.is_active,
                    planned_start_date=instance_data.planned_start_date,
                    planned_end_date=instance_data.planned_end_date,
                    estimated_duration_minutes=instance_data.estimated_duration_minutes,
                    assigned_user_id=instance_data.assigned_user_id,
                    supervisor_user_id=instance_data.supervisor_user_id,
                    notes=instance_data.notes
                )
                instances.append(craft_instance)

            created_instances = await self.order_craft_instance_repository.bulk_create(instances)
            
            return OrderCraftInstanceOperationResultDTO(
                success=True,
                message=f"Successfully created {len(created_instances)} craft instances",
                instance_ids=[instance.id for instance in created_instances]
            )
        except Exception as e:
            return OrderCraftInstanceOperationResultDTO(
                success=False,
                message=f"Failed to bulk create craft instances: {str(e)}"
            )

    async def bulk_update_craft_instances(
        self, 
        bulk_update: OrderCraftInstanceBulkUpdateDTO
    ) -> OrderCraftInstanceOperationResultDTO:
        """Bulk update order craft instances."""
        try:
            # Update each instance individually for proper validation
            updated_instances = []
            for instance_id in bulk_update.instance_ids:
                updated = await self.update_craft_instance(instance_id, bulk_update.update_data)
                if updated:
                    updated_instances.append(updated)

            return OrderCraftInstanceOperationResultDTO(
                success=True,
                message=f"Successfully updated {len(updated_instances)} craft instances",
                instance_ids=bulk_update.instance_ids
            )
        except Exception as e:
            return OrderCraftInstanceOperationResultDTO(
                success=False,
                message=f"Failed to bulk update craft instances: {str(e)}"
            )

    # Query operations
    async def get_craft_instances_by_order(
        self, 
        order_no: str, 
        factory_id: int
    ) -> OrderCraftInstanceListDTO:
        """Get all craft instances for an order."""
        instances = await self.order_craft_instance_repository.get_by_order_no(order_no, factory_id)
        
        response_dtos = []
        for instance in instances:
            dto = await self._build_instance_response_dto(instance)
            response_dtos.append(dto)

        return OrderCraftInstanceListDTO(instances=response_dtos, total=len(response_dtos))

    async def get_craft_instances_by_part(
        self, 
        order_part_no: str, 
        factory_id: int
    ) -> OrderCraftInstanceListDTO:
        """Get all craft instances for an order part."""
        instances = await self.order_craft_instance_repository.get_by_order_part_no(order_part_no, factory_id)
        
        response_dtos = []
        for instance in instances:
            dto = await self._build_instance_response_dto(instance)
            response_dtos.append(dto)

        return OrderCraftInstanceListDTO(instances=response_dtos, total=len(response_dtos))

    async def get_craft_instances_by_user(
        self, 
        user_id: int, 
        factory_id: int, 
        status: Optional[OrderCraftInstanceStatusDTO] = None
    ) -> OrderCraftInstanceListDTO:
        """Get craft instances assigned to a user."""
        status_enum = OrderCraftInstanceStatus(status.value) if status else None
        instances = await self.order_craft_instance_repository.get_by_assigned_user(user_id, factory_id, status_enum)
        
        response_dtos = []
        for instance in instances:
            dto = await self._build_instance_response_dto(instance)
            response_dtos.append(dto)

        return OrderCraftInstanceListDTO(instances=response_dtos, total=len(response_dtos))

    async def search_craft_instances(
        self, 
        search: OrderCraftInstanceSearchDTO, 
        factory_id: int, 
        skip: int = 0, 
        limit: int = 100
    ) -> OrderCraftInstanceListDTO:
        """Search craft instances with flexible criteria."""
        search_params = {
            "search_term": search.search_term,
            "status": OrderCraftInstanceStatus(search.status.value) if search.status else None,
            "part_type": search.part_type,
            "craft_code": search.craft_code,
            "assigned_user_id": search.assigned_user_id,
            "supervisor_id": search.supervisor_id,
            "start_date": search.start_date,
            "end_date": search.end_date
        }
        
        # Remove None values
        search_params = {k: v for k, v in search_params.items() if v is not None}
        
        instances = await self.order_craft_instance_repository.search_instances(
            factory_id, search_params, skip, limit
        )
        
        response_dtos = []
        for instance in instances:
            dto = await self._build_instance_response_dto(instance)
            response_dtos.append(dto)

        return OrderCraftInstanceListDTO(instances=response_dtos, total=len(response_dtos))

    # Workflow and analytics
    async def get_part_workflow_status(
        self, 
        order_no: str, 
        order_part_no: str, 
        factory_id: int
    ) -> OrderCraftInstanceWorkflowDTO:
        """Get workflow status for a specific order part."""
        instances = await self.order_craft_instance_repository.get_workflow_status(order_no, order_part_no, factory_id)
        
        workflow_dtos = []
        current_step = None
        next_steps = []
        completed_count = 0
        
        for instance in instances:
            dto = await self._build_instance_response_dto(instance)
            workflow_dtos.append(dto)
            
            if instance.is_completed():
                completed_count += 1
            elif instance.is_in_progress() and not current_step:
                current_step = dto
            elif instance.is_pending() and instance.can_start():
                next_steps.append(dto)

        part_type = instances[0].part_type if instances else ""
        total_steps = len(instances)
        overall_progress = (completed_count / total_steps * 100) if total_steps > 0 else 0

        return OrderCraftInstanceWorkflowDTO(
            order_no=order_no,
            order_part_no=order_part_no,
            part_type=part_type,
            workflow_instances=workflow_dtos,
            current_step=current_step,
            next_steps=next_steps[:3],  # Limit to next 3 steps
            total_steps=total_steps,
            completed_steps=completed_count,
            overall_progress=overall_progress
        )

    async def get_craft_instance_statistics(
        self, 
        factory_id: int, 
        start_date: Optional[datetime] = None, 
        end_date: Optional[datetime] = None
    ) -> OrderCraftInstanceStatisticsDTO:
        """Get statistics for craft instances."""
        performance_data = await self.order_craft_instance_repository.get_performance_metrics(
            factory_id, start_date, end_date
        )
        
        return OrderCraftInstanceStatisticsDTO(
            total=performance_data["total"],
            completed=performance_data["completed"],
            in_progress=0,  # Will be calculated separately
            pending=0,      # Will be calculated separately
            on_hold=0,      # Will be calculated separately
            skipped=0,      # Will be calculated separately
            failed=0,       # Will be calculated separately
            completion_rate=performance_data["completion_rate"],
            avg_progress=performance_data["avg_progress"],
            avg_duration_minutes=performance_data["avg_duration_minutes"],
            avg_quality_score=performance_data["avg_quality_score"],
            total_rework=performance_data["total_rework"],
            total_defects=performance_data["total_defects"],
            overdue_count=performance_data["overdue_count"],
            on_time_rate=performance_data["on_time_rate"]
        )

    async def get_craft_instance_dashboard(self, factory_id: int) -> OrderCraftInstanceDashboardDTO:
        """Get dashboard data for craft instances."""
        # Get overall statistics
        statistics = await self.get_craft_instance_statistics(factory_id)
        
        # Get overdue instances
        overdue_instances_entities = await self.order_craft_instance_repository.get_overdue_instances(factory_id)
        overdue_instances = []
        for instance in overdue_instances_entities[:10]:  # Limit to 10
            dto = await self._build_instance_response_dto(instance)
            overdue_instances.append(dto)
        
        # Get in-progress instances
        in_progress_entities = await self.order_craft_instance_repository.get_in_progress_instances(factory_id)
        in_progress_instances = []
        for instance in in_progress_entities[:10]:  # Limit to 10
            dto = await self._build_instance_response_dto(instance)
            in_progress_instances.append(dto)
        
        # Get recent completions
        recent_completed = await self.order_craft_instance_repository.get_by_status(
            OrderCraftInstanceStatus.COMPLETED, factory_id, 0, 10
        )
        recent_completions = []
        for instance in recent_completed:
            dto = await self._build_instance_response_dto(instance)
            recent_completions.append(dto)
        
        # Get performance by craft and part type
        performance_by_craft = await self.order_craft_instance_repository.get_statistics_by_craft(factory_id)
        performance_by_part_type = await self.order_craft_instance_repository.get_statistics_by_part_type(factory_id)
        
        return OrderCraftInstanceDashboardDTO(
            statistics=statistics,
            overdue_instances=overdue_instances,
            in_progress_instances=in_progress_instances,
            recent_completions=recent_completions,
            performance_by_craft=performance_by_craft,
            performance_by_part_type=performance_by_part_type,
            quality_trends={}  # Would need additional queries for trends
        )

    # Helper methods
    async def _build_instance_response_dto(self, instance: OrderCraftInstance) -> OrderCraftInstanceResponseDTO:
        """Build OrderCraftInstanceResponseDTO from entity."""
        dto = OrderCraftInstanceResponseDTO.model_validate(instance)
        
        # Add user names
        if instance.assigned_user:
            dto.assigned_user_name = instance.assigned_user.full_name
        if instance.supervisor:
            dto.supervisor_name = instance.supervisor.full_name
        
        # Calculate derived fields
        dto.duration_minutes = instance.get_duration_minutes()
        dto.is_overdue = instance.is_overdue()
        
        return dto

    async def _build_instance_detail_dto(self, instance: OrderCraftInstance) -> OrderCraftInstanceDetailDTO:
        """Build OrderCraftInstanceDetailDTO from entity."""
        base_dto = await self._build_instance_response_dto(instance)
        detail_dto = OrderCraftInstanceDetailDTO(**base_dto.model_dump())
        
        # Add detailed information
        if instance.order:
            detail_dto.order_skc_no = instance.order.skc_no
            detail_dto.order_status = instance.order.status.value
        
        if instance.order_part:
            detail_dto.part_name = instance.order_part.part_name
            detail_dto.part_sequence = instance.order_part.part_sequence
            detail_dto.part_total_quantity = instance.order_part.total_quantity
        
        if instance.order_craft:
            detail_dto.craft_name = instance.order_craft.craft_name
            detail_dto.craft_estimated_duration = instance.order_craft.estimated_duration_hours
        
        # Add route instances information
        detail_dto.total_route_instances = instance.get_total_route_instances_count()
        detail_dto.completed_route_instances = instance.get_completed_route_instances_count()
        detail_dto.route_completion_percentage = instance.calculate_route_completion_percentage()
        
        return detail_dto