from typing import List, Optional
from datetime import datetime, timezone
from src.application.interfaces.order_craft_repository_interface import OrderCraftRepositoryInterface
from src.application.interfaces.order_craft_route_repository_interface import OrderCraftRouteRepositoryInterface
from src.application.interfaces.order_repository_interface import OrderRepositoryInterface
from src.application.interfaces.craft_repository_interface import CraftRepositoryInterface
from src.application.interfaces.skill_repository_interface import SkillRepositoryInterface
from src.application.interfaces.user_repository_interface import UserRepositoryInterface
from src.application.dto.order_craft_dto import (
    OrderCraftCreateDTO, OrderCraftResponseDTO,
    OrderCraftRouteResponseDTO,
    OrderCraftStatusUpdateDTO, Order<PERSON>raftRouteStatusUpdateDTO,
    OrderCraftOperationResultDTO, OrderCraftStatisticsDTO
)
from src.domain.entities.order_craft import OrderCraft
from src.domain.entities.order_craft_route import OrderCraftRoute


class OrderCraftUseCases:
    """Use cases for order craft management operations."""
    
    def __init__(
        self,
        order_craft_repository: OrderCraftRepositoryInterface,
        order_craft_route_repository: OrderCraftRouteRepositoryInterface,
        order_repository: OrderRepositoryInterface,
        craft_repository: CraftRepositoryInterface,
        skill_repository: SkillRepositoryInterface,
        user_repository: UserRepositoryInterface
    ):
        self.order_craft_repository = order_craft_repository
        self.order_craft_route_repository = order_craft_route_repository
        self.order_repository = order_repository
        self.craft_repository = craft_repository
        self.skill_repository = skill_repository
        self.user_repository = user_repository
    
    async def create_order_crafts_for_order(
        self, 
        order_no: str, 
        current_factory_id: int,
        order_craft_configs: List[OrderCraftCreateDTO]
    ) -> List[OrderCraftResponseDTO]:
        """Create order crafts configuration for an order using merge logic."""
        # Verify order exists
        order = await self.order_repository.get_by_order_no(order_no)
        if not order:
            raise ValueError(f"Order {order_no} not found")

        from src.infrastructure.database.database import Database
        from config import settings
        db = Database(settings.database.url)
        
        # Use a single database transaction for all operations
        async with db.session_factory() as session:
            try:
                order_crafts = []
                
                for config in order_craft_configs:
                    # Validate craft exists
                    craft = await self.craft_repository.get_by_code(config.craft_code)
                    if not craft:
                        raise ValueError(f"Craft {config.craft_code} not found")
                    
                    # Validate part_type consistency with order
                    if order.part_types and config.part_type not in order.part_types:
                        raise ValueError(f"Part type {config.part_type} is not required by order {order_no}. Order requires: {order.part_types}")
                    
                    # Create order craft with proper defaults
                    order_craft = OrderCraft(
                        order_no=order_no,
                        order_id=order.id,  # Set the order ID
                        factory_id=current_factory_id,
                        craft_code=config.craft_code,
                        part_type=config.part_type,
                        craft_name=config.craft_name,
                        order=config.order,
                        is_required=config.is_required,
                        is_active=True,  # Default value
                        status="pending",  # Default value
                        estimated_duration_hours=config.estimated_duration_hours,
                        notes=config.notes,
                        created_at=datetime.now(timezone.utc),
                        updated_at=datetime.now(timezone.utc)
                    )
                    
                    order_crafts.append(order_craft)
                
                # Bulk upsert order crafts (merge logic: update if exists, create if not)
                upserted_order_crafts = await self.order_craft_repository.bulk_upsert(order_crafts, session)
                
                # Create mapping from craft_code to order_craft for route creation
                craft_code_to_order_craft = {craft.craft_code: craft for craft in upserted_order_crafts}
                
                # Prepare order craft routes for all order crafts
                all_order_craft_routes = []
                
                for config in order_craft_configs:
                    order_craft = craft_code_to_order_craft[config.craft_code]
                    route_configs = config.order_craft_routes
                    
                    # Ensure the order_craft has an ID before creating routes
                    if order_craft.id is None:
                        raise ValueError(f"OrderCraft for {order_craft.craft_code} does not have an ID after upsert")
                    
                    for route_config in route_configs:
                        # Validate skill exists
                        skill = await self.skill_repository.get_by_code(route_config.skill_code)
                        if not skill:
                            raise ValueError(f"Skill {route_config.skill_code} not found")
                        
                        # Validate assigned user if specified
                        if route_config.assigned_user_id:
                            user = await self.user_repository.get_by_id(route_config.assigned_user_id)
                            if not user:
                                raise ValueError(f"User {route_config.assigned_user_id} not found")
                        
                        # Validate part_type consistency with parent order craft
                        if route_config.part_type != order_craft.part_type:
                            raise ValueError(f"Route part type {route_config.part_type} does not match order craft part type {order_craft.part_type}")
                        
                        order_craft_route = OrderCraftRoute(
                            order_craft_id=order_craft.id,
                            factory_id=current_factory_id,
                            skill_code=route_config.skill_code,
                            part_type=route_config.part_type,
                            name=route_config.name,
                            code=route_config.code,
                            order=route_config.order,
                            measurement_types=route_config.measurement_types,
                            registration_types=route_config.registration_types,
                            is_required=route_config.is_required,
                            is_active=True,  # Default value
                            status="pending",  # Default value
                            assigned_user_id=route_config.assigned_user_id,
                            estimated_duration_minutes=route_config.estimated_duration_minutes,
                            price=route_config.price,
                            total_cost=route_config.total_cost,
                            notes=route_config.notes,
                            created_at=datetime.now(timezone.utc),
                            updated_at=datetime.now(timezone.utc)
                        )
                        
                        all_order_craft_routes.append(order_craft_route)
                
                # Bulk upsert order craft routes (merge logic: update if exists, create if not)
                if all_order_craft_routes:
                    await self.order_craft_route_repository.bulk_upsert(all_order_craft_routes, session)
                
                # Commit the transaction
                await session.commit()
                
                # Return response DTOs
                response_dtos = []
                for order_craft in upserted_order_crafts:
                    dto = await self._build_order_craft_dto(order_craft)
                    response_dtos.append(dto)
                
                return response_dtos
                
            except Exception as e:
                await session.rollback()
                raise e
    
    async def get_order_crafts_by_order(self, order_no: str) -> List[OrderCraftResponseDTO]:
        """Get all order crafts for an order."""
        order_crafts = await self.order_craft_repository.get_by_order_no(order_no)
        
        response_dtos = []
        for order_craft in order_crafts:
            dto = await self._build_order_craft_dto(order_craft)
            response_dtos.append(dto)
        
        return response_dtos
    
    async def get_order_craft_by_id(self, order_craft_id: int) -> Optional[OrderCraftResponseDTO]:
        """Get order craft by ID."""
        order_craft = await self.order_craft_repository.get_by_id(order_craft_id)
        if not order_craft:
            return None
        
        return await self._build_order_craft_dto(order_craft)
    
    async def update_order_craft_status(
        self, 
        order_craft_id: int, 
        status_data: OrderCraftStatusUpdateDTO
    ) -> OrderCraftOperationResultDTO:
        """Update order craft status."""
        order_craft = await self.order_craft_repository.get_by_id(order_craft_id)
        if not order_craft:
            return OrderCraftOperationResultDTO(
                success=False,
                message="Order craft not found",
                order_craft_id=order_craft_id,
                order_craft_route_id=None,
                details=None
            )
        
        old_status = order_craft.status
        
        # Handle status transitions
        if status_data.status == "in_progress":
            order_craft.start_craft()
        elif status_data.status == "completed":
            order_craft.complete_craft()
        elif status_data.status == "skipped":
            order_craft.skip_craft(status_data.notes)
        elif status_data.status == "pending":
            order_craft.reset_craft()
        else:
            order_craft.status = status_data.status
            order_craft.updated_at = datetime.now(timezone.utc)
        
        if status_data.notes and status_data.status != "skipped":
            order_craft.notes = f"{order_craft.notes or ''}\n[{datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M')}] Status changed from {old_status} to {status_data.status}: {status_data.notes}"
        
        await self.order_craft_repository.update(order_craft)
        
        return OrderCraftOperationResultDTO(
            success=True,
            message=f"Order craft status updated to {status_data.status}",
            order_craft_id=order_craft_id,
            order_craft_route_id=None,
            details={"old_status": old_status, "new_status": status_data.status}
        )
    
    async def update_order_craft_route_status(
        self, 
        order_craft_route_id: int, 
        status_data: OrderCraftRouteStatusUpdateDTO
    ) -> OrderCraftOperationResultDTO:
        """Update order craft route status."""
        order_craft_route = await self.order_craft_route_repository.get_by_id(order_craft_route_id)
        if not order_craft_route:
            return OrderCraftOperationResultDTO(
                success=False,
                message="Order craft route not found",
                order_craft_id=None,
                order_craft_route_id=order_craft_route_id,
                details=None
            )
        
        old_status = order_craft_route.status
        
        # Handle status transitions
        if status_data.status == "in_progress":
            order_craft_route.start_route()
        elif status_data.status == "completed":
            order_craft_route.complete_route(
                quality_score=status_data.quality_score,
                completion_notes=status_data.completion_notes
            )
        elif status_data.status == "skipped":
            order_craft_route.skip_route(status_data.completion_notes)
        elif status_data.status == "pending":
            order_craft_route.reset_route()
        else:
            order_craft_route.status = status_data.status
            order_craft_route.updated_at = datetime.now(timezone.utc)
        
        await self.order_craft_route_repository.update(order_craft_route)
        
        return OrderCraftOperationResultDTO(
            success=True,
            message=f"Order craft route status updated to {status_data.status}",
            order_craft_id=None,
            order_craft_route_id=order_craft_route_id,
            details={"old_status": old_status, "new_status": status_data.status}
        )
    
    async def get_next_craft_for_order(self, order_no: str) -> Optional[OrderCraftResponseDTO]:
        """Get the next pending craft for an order."""
        order_craft = await self.order_craft_repository.get_next_pending_craft(order_no)
        if not order_craft:
            return None
        
        return await self._build_order_craft_dto(order_craft)
    
    async def get_current_craft_for_order(self, order_no: str) -> Optional[OrderCraftResponseDTO]:
        """Get the currently in-progress craft for an order."""
        order_craft = await self.order_craft_repository.get_current_in_progress_craft(order_no)
        if not order_craft:
            return None
        
        return await self._build_order_craft_dto(order_craft)
    
    async def get_order_craft_statistics(self, order_no: Optional[str] = None) -> OrderCraftStatisticsDTO:
        """Get order craft statistics."""
        stats = await self.order_craft_repository.get_order_craft_statistics(order_no)
        
        return OrderCraftStatisticsDTO(
            total_order_crafts=stats.get("total_order_crafts", 0),
            pending_crafts=stats.get("pending_crafts", 0),
            in_progress_crafts=stats.get("in_progress_crafts", 0),
            completed_crafts=stats.get("completed_crafts", 0),
            skipped_crafts=stats.get("skipped_crafts", 0),
            active_crafts=stats.get("active_crafts", 0),
            inactive_crafts=stats.get("inactive_crafts", 0),
            status_breakdown=stats.get("status_breakdown", {}),
            active_breakdown=stats.get("active_breakdown", {})
        )
    
    async def _build_order_craft_dto(self, order_craft: OrderCraft) -> OrderCraftResponseDTO:
        """Build OrderCraftResponseDTO from OrderCraft entity."""
        # Get craft name
        craft_name = None
        if order_craft.craft:
            craft_name = order_craft.craft.name
        
        # Get order craft routes
        routes = await self.order_craft_route_repository.get_by_order_craft_id(order_craft.id)
        route_dtos = []
        
        for route in routes:
            route_dto = await self._build_order_craft_route_dto(route)
            route_dtos.append(route_dto)
        
        dto = OrderCraftResponseDTO.model_validate(order_craft)
        dto.craft_name = craft_name
        dto.completion_percentage = order_craft.get_completion_percentage()
        dto.order_craft_routes = route_dtos
        
        return dto
    
    async def _build_order_craft_route_dto(self, order_craft_route: OrderCraftRoute) -> OrderCraftRouteResponseDTO:
        """Build OrderCraftRouteResponseDTO from OrderCraftRoute entity."""
        # Get skill name
        skill_name = None
        if order_craft_route.skill:
            skill_name = order_craft_route.skill.name
        
        # Get assigned user name
        assigned_user_name = None
        if order_craft_route.assigned_user:
            assigned_user_name = order_craft_route.assigned_user.full_name or order_craft_route.assigned_user.username
        
        dto = OrderCraftRouteResponseDTO.model_validate(order_craft_route)
        dto.skill_name = skill_name
        dto.assigned_user_name = assigned_user_name
        
        return dto

    # Part type management methods
    async def get_order_crafts_by_part_type(
        self, 
        order_no: str, 
        part_type: str, 
        factory_id: int
    ) -> List[OrderCraftResponseDTO]:
        """Get order crafts filtered by part type."""
        order_crafts = await self.order_craft_repository.get_by_order_and_factory(order_no, factory_id)
        
        # Filter by part type
        filtered_crafts = [craft for craft in order_crafts if craft.part_type == part_type]
        
        # Build response DTOs
        response_dtos = []
        for order_craft in filtered_crafts:
            dto = await self._build_order_craft_dto(order_craft)
            response_dtos.append(dto)
        
        return response_dtos

    async def get_order_craft_routes_by_part_type(
        self, 
        order_no: str, 
        part_type: str, 
        factory_id: int
    ) -> List[OrderCraftRouteResponseDTO]:
        """Get order craft routes filtered by part type."""
        # First get all order crafts for the order and factory
        order_crafts = await self.order_craft_repository.get_by_order_and_factory(order_no, factory_id)
        
        # Filter by part type and collect routes
        all_routes = []
        for craft in order_crafts:
            if craft.part_type == part_type:
                routes = await self.order_craft_route_repository.get_by_order_craft_id(craft.id)
                all_routes.extend(routes)
        
        # Build response DTOs
        response_dtos = []
        for route in all_routes:
            dto = await self._build_order_craft_route_dto(route)
            response_dtos.append(dto)
        
        return response_dtos

    async def validate_part_type_consistency(self, order_no: str, factory_id: int) -> dict:
        """Validate part type consistency across Order, OrderCraft, and OrderCraftRoute."""
        # Get order
        order = await self.order_repository.get_by_order_no_and_factory(order_no, factory_id)
        if not order:
            raise ValueError(f"Order {order_no} not found")
        
        # Get order crafts
        order_crafts = await self.order_craft_repository.get_by_order_and_factory(order_no, factory_id)
        
        validation_issues = []
        
        # Check order craft part types against order part types
        if order.part_types:
            for craft in order_crafts:
                if craft.part_type not in order.part_types:
                    validation_issues.append({
                        "type": "order_craft_inconsistency",
                        "message": f"OrderCraft {craft.craft_code} has part_type {craft.part_type} which is not in order part_types: {order.part_types}",
                        "order_craft_id": craft.id
                    })
        
        # Check order craft route part types against order craft part types
        for craft in order_crafts:
            routes = await self.order_craft_route_repository.get_by_order_craft_id(craft.id)
            for route in routes:
                if route.part_type != craft.part_type:
                    validation_issues.append({
                        "type": "order_craft_route_inconsistency",
                        "message": f"OrderCraftRoute {route.skill_code} has part_type {route.part_type} which differs from OrderCraft part_type {craft.part_type}",
                        "order_craft_route_id": route.id,
                        "order_craft_id": craft.id
                    })
        
        return {
            "valid": len(validation_issues) == 0,
            "issues": validation_issues,
            "order_no": order_no
        }