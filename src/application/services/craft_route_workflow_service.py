from typing import Optional, List, Dict, Any, Tuple, Set
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from sqlalchemy.orm import selectinload
import logging
from datetime import datetime, timezone

logger = logging.getLogger(__name__)
from src.domain.entities.order_craft_route_instance import OrderCraftRouteInstance, CompletionGranularity
from src.domain.entities.order_craft_route import OrderCraftRoute
from src.domain.entities.order_craft import OrderCraft
from src.domain.entities.order_bundle import OrderBundle
from src.domain.entities.order_part import OrderPart
from src.domain.entities.order_line import OrderLine
from src.domain.entities.order import Order, OrderStatus
from src.application.interfaces.order_bundle_repository_interface import OrderBundleRepositoryInterface
from src.application.interfaces.order_part_repository_interface import OrderPartRepositoryInterface
from src.application.interfaces.order_line_repository_interface import OrderLineRepositoryInterface
from src.application.interfaces.order_repository_interface import OrderRepositoryInterface
from src.application.interfaces.order_craft_route_repository_interface import OrderCraftRouteRepositoryInterface
from src.application.interfaces.order_craft_route_instance_repository_interface import OrderCraftRouteInstanceRepositoryInterface


class CraftRouteWorkflowService:
    """工艺路线工作流服务 - 处理完成实例后的状态更新和阶段推进
    0. collect order_parts_array
    

    1. 遍历craft,
    2. 遍历craft_route,
    3. init is_order_completed, completed_parts, completed_part_bundles
    4. is_pending = len(遍历craft_route_instances) > 0
    5. min_start_time = min[x.started_at for x in craft_route_instances if x.started_at]
    6. max_end_time = max[x.completed_at for x in craft_route_instances if x.completed_at]
    7. 遍历craft_route_instance,
        1. update completed_parts, completed_part_bundles
        2. update interest(completed_part_bundles, parts[N].bundles) = parts[N].bundles, then push parts[N] to completed_parts
    8. check is_order_completed, completed_parts, completed_part_bundles
    9. check craft_route_status
        9.1 if is_order_completed, then craft_route completed
        9.2 if completed_parts = all_order_parts, then order_part completed
    10. if some craft_route_status is pending, then craft_status = pending
    11. if all craft_route_status is finished, then craft_status = finished
    12. check order

    """
    
    def __init__(
        self,
        order_bundle_repository: OrderBundleRepositoryInterface,
        order_part_repository: OrderPartRepositoryInterface,
        order_line_repository: OrderLineRepositoryInterface,
        order_repository: OrderRepositoryInterface,
        order_craft_route_repository: OrderCraftRouteRepositoryInterface,
        instance_repository: OrderCraftRouteInstanceRepositoryInterface
    ):
        self.order_bundle_repository = order_bundle_repository
        self.order_part_repository = order_part_repository
        self.order_line_repository = order_line_repository
        self.order_repository = order_repository
        self.order_craft_route_repository = order_craft_route_repository
        self.instance_repository = instance_repository
    
    async def process_completion_instance(
        self, 
        completion_instance: OrderCraftRouteInstance,
        session: AsyncSession
    ) -> Dict[str, Any]:
        """
        处理完成实例，更新相关实体状态和工艺路线阶段
        
        Args:
            completion_instance: 完成实例
            session: 数据库会话
            
        Returns:
            Dict包含更新结果和影响的实体
        """
        result = {
            "success": True,
            "message": "Processing completed successfully",
            "updated_entities": [],
            "advanced_stages": [],
            "errors": []
        }
        
        try:
            logger.info(f"Processing completion instance {completion_instance.id} for route {completion_instance.order_craft_route_id}")
            
            # 1. 重新查询工艺路线实体（确保在当前session中并加载所需关系）
            try:
                craft_route_id = completion_instance.order_craft_route_id
                logger.info(f"Loading craft route {craft_route_id} with relationships")
                
                # Query craft_route with all needed relationships in current session
                stmt = (
                    select(OrderCraftRoute)
                    .options(
                        selectinload(OrderCraftRoute.order_craft),
                        selectinload(OrderCraftRoute.skill),
                        selectinload(OrderCraftRoute.assigned_user)
                    )
                    .where(OrderCraftRoute.id == craft_route_id)
                )
                db_result = await session.execute(stmt)
                craft_route = db_result.scalar_one_or_none()
                
                if not craft_route:
                    raise ValueError(f"Craft route {craft_route_id} not found")
                    
                logger.info(f"Loaded craft route {craft_route.id} with status: {craft_route.status}")
            except Exception as e:
                logger.error(f"Failed to load craft route: {str(e)}", exc_info=True)
                raise
            
            # 2. 如果工艺路线是pending状态，启动它并设置craft为pending状态
            try:
                if craft_route.status == "pending":
                    logger.info(f"Starting craft route {craft_route.id}")
                    craft_route.start_route(completion_instance.worker_user_id)
                    
                    # 设置craft为pending状态
                    order_craft = craft_route.order_craft
                    if order_craft.status != "pending":
                        order_craft.reset_craft()
                        logger.info(f"Set order craft {order_craft.id} to pending status")
                        result["updated_entities"].append(f"Set craft {order_craft.craft_code} to pending")
                    
                    result["updated_entities"].append(f"Started craft route: {craft_route.code}")
            except Exception as e:
                logger.error(f"Failed to start craft route {craft_route.id}: {str(e)}", exc_info=True)
                raise
            
            # 3. 检查工艺路线是否完成
            try:
                craft_route_complete = await self._check_craft_route_completion(
                    completion_instance, session
                )
                logger.info(f"Craft route completion check result: {craft_route_complete}")
            except Exception as e:
                logger.error(f"Failed to check craft route completion: {str(e)}", exc_info=True)
                raise
            
            # 4. 如果工艺路线完成，标记为完成并更新craft的当前工艺路线
            try:
                if craft_route_complete and craft_route.status == "in_progress":
                    logger.info(f"Completing craft route {craft_route.id}")
                    craft_route.complete_route()
                    result["updated_entities"].append(f"Completed craft route: {craft_route.code}")
                    
                    # 查找该craft下最小的pending工艺路线
                    await self._update_craft_current_route(craft_route.order_craft, session, result)
            except Exception as e:
                logger.error(f"Failed to complete craft route {craft_route.id}: {str(e)}", exc_info=True)
                raise
            
            # 5. 根据完成粒度更新相关实体
            try:
                if completion_instance.is_bundle_level():
                    await self._update_bundle_status(completion_instance, session, result)
                elif completion_instance.is_bed_level():
                    await self._update_bed_status(completion_instance, session, result)
                elif completion_instance.is_order_level():
                    await self._update_order_status(completion_instance, session, result)
            except Exception as e:
                logger.error(f"Failed to update entity status: {str(e)}", exc_info=True)
                raise
            
            # 6. 如果工艺路线完成，推进到下一阶段
            try:
                if craft_route_complete:
                    await self._advance_to_next_craft_route(
                        completion_instance, session, result
                    )
            except Exception as e:
                logger.error(f"Failed to advance to next craft route: {str(e)}", exc_info=True)
                raise
            
            # 7. 更新父级实体状态
            try:
                await self._update_parent_entity_status(completion_instance, session, result)
            except Exception as e:
                logger.error(f"Failed to update parent entity status: {str(e)}", exc_info=True)
                raise
            
        except Exception as e:
            logger.error(
                f"Error processing completion instance {completion_instance.id}: {type(e).__name__}: {str(e)}",
                exc_info=True,
                extra={
                    "error": str(e),
                    "error_type": type(e).__name__,
                    "completion_instance_id": completion_instance.id,
                    "order_craft_route_id": completion_instance.order_craft_route_id,
                    "worker_user_id": completion_instance.worker_user_id,
                    "completion_granularity": completion_instance.completion_granularity.value if completion_instance.completion_granularity else None,
                    "order_no": completion_instance.order_no,
                    "order_part_nos": completion_instance.order_part_nos,
                    "order_bundle_nos": completion_instance.order_bundle_nos
                }
            )

            result["success"] = False
            result["message"] = f"Processing failed: {str(e)}"
            result["errors"].append(str(e))
        
        return result
    
    async def _check_craft_route_completion(
        self, 
        completion_instance: OrderCraftRouteInstance,
        session: AsyncSession
    ) -> bool:
        """检查工艺路线是否完成"""
        # 获取该工艺路线的所有完成实例
        craft_route = completion_instance.order_craft_route
        
        # 根据完成粒度计算是否完成
        if completion_instance.is_bundle_level():
            return await self._check_bundle_craft_route_completion(
                craft_route, completion_instance.order_bundle_no, session
            )
        elif completion_instance.is_bed_level():
            return await self._check_bed_craft_route_completion(
                craft_route, completion_instance.order_part_no, session
            )
        else:  # order level
            return await self._check_order_craft_route_completion(
                craft_route, completion_instance.order_no, session
            )
    
    async def _check_bundle_craft_route_completion(
        self, 
        craft_route: OrderCraftRoute,
        order_bundle_no: str,
        session: AsyncSession
    ) -> bool:
        """检查扎级别工艺路线是否完成"""
        # 获取扎的信息
        bundle = await self.order_bundle_repository.get_by_order_bundle_no_and_factory(
            order_bundle_no, craft_route.factory_id, craft_route.order_craft.order_no, session
        )
        if not bundle:
            return False
        
        # 获取该工艺路线的所有实例
        instances = await self.instance_repository.get_by_craft_route(
            craft_route.id, session
        )
        
        # 计算该工艺路线在此扎上的完成数量
        completed_quantity = sum(
            instance.completed_quantity 
            for instance in instances
            if (instance.order_bundle_no == order_bundle_no and 
                instance.status == "completed")
        )
        
        # 如果完成数量达到扎的总数量，则认为完成
        return completed_quantity >= bundle.quantity
    
    async def _check_bed_craft_route_completion(
        self, 
        craft_route: OrderCraftRoute,
        order_part_no: str,
        session: AsyncSession
    ) -> bool:
        """检查床级别工艺路线是否完成"""
        # 获取床的所有扎
        bundles = await self.order_bundle_repository.get_by_order_part_no_and_factory(
            order_part_no, craft_route.factory_id, craft_route.order_craft.order_no, session
        )
        
        total_quantity = sum(bundle.quantity for bundle in bundles)
        
        # 获取该工艺路线的所有实例
        instances = await self.instance_repository.get_by_craft_route(
            craft_route.id, session
        )
        
        # 计算该工艺路线在此床上的完成数量
        completed_quantity = sum(
            instance.completed_quantity 
            for instance in instances
            if (instance.order_part_no == order_part_no and 
                instance.status == "completed")
        )
        
        return completed_quantity >= total_quantity
    
    async def _check_order_craft_route_completion(
        self, 
        craft_route: OrderCraftRoute,
        order_no: str,
        session: AsyncSession
    ) -> bool:
        """检查整单级别工艺路线是否完成"""
        # 获取订单的总数量
        order = await self.order_repository.get_by_order_no_and_factory(
            order_no, craft_route.factory_id, session
        )
        if not order:
            return False
        
        # 获取该工艺路线的所有实例
        instances = await self.instance_repository.get_by_craft_route(
            craft_route.id, session
        )
        
        # 计算该工艺路线在此订单上的完成数量
        completed_quantity = sum(
            instance.completed_quantity 
            for instance in instances
            if (instance.order_no == order_no and 
                instance.status == "completed")
        )
        
        # 假设订单有总数量字段，如果没有则需要计算所有bundle的总数量
        # 这里先返回True作为简化实现，实际需要根据业务逻辑确定完成条件
        return completed_quantity > 0  # 简化版：有完成数量就认为完成
    
    async def _update_bundle_status(
        self, 
        completion_instance: OrderCraftRouteInstance,
        session: AsyncSession,
        result: Dict[str, Any]
    ) -> None:
        """更新扎状态"""
        bundle = await self.order_bundle_repository.get_by_order_bundle_no_and_factory(
            completion_instance.order_bundle_no,
            completion_instance.factory_id,
            completion_instance.order_no,
            session
        )
        
        if bundle:
            # 更新当前工艺路线代码
            bundle.current_craft_route_code = completion_instance.order_craft_route.code
            # 更新完成数量
            bundle.completed_quantity += completion_instance.completed_quantity
            bundle.update_progress()
            
            await self.order_bundle_repository.update(bundle)
            result["updated_entities"].append(f"Bundle: {bundle.order_bundle_no}")
    
    async def _update_bed_status(
        self, 
        completion_instance: OrderCraftRouteInstance,
        session: AsyncSession,
        result: Dict[str, Any]
    ) -> None:
        """更新床状态"""
        # 更新床下所有扎的状态
        bundles = await self.order_bundle_repository.get_by_order_part_no_and_factory(
            completion_instance.order_part_no,
            completion_instance.factory_id,
            completion_instance.order_no,
            session
        )
        
        for bundle in bundles:
            bundle.current_craft_route_code = completion_instance.order_craft_route.code
            await self.order_bundle_repository.update(bundle)
        
        # 更新订单部位状态
        order_part = await self.order_part_repository.get_by_order_part_no_and_factory(
            completion_instance.order_part_no,
            completion_instance.factory_id,
            completion_instance.order_no,
            session
        )
        
        if order_part:
            order_part.current_craft_route_code = completion_instance.order_craft_route.code
            await self.order_part_repository.update(order_part)
            result["updated_entities"].append(f"OrderPart: {order_part.order_part_no}")
    
    async def _update_order_status(
        self, 
        completion_instance: OrderCraftRouteInstance,
        session: AsyncSession,
        result: Dict[str, Any]
    ) -> None:
        """更新整单状态"""
        # 更新订单状态
        order = await self.order_repository.get_by_order_no_and_factory(
            completion_instance.order_no,
            completion_instance.factory_id,
            session
        )
        
        if order:
            order.current_craft_route_code = completion_instance.order_craft_route.code
            await self.order_repository.update(order)
            result["updated_entities"].append(f"Order: {order.order_no}")
    
    async def _advance_to_next_craft_route(
        self, 
        completion_instance: OrderCraftRouteInstance,
        session: AsyncSession,
        result: Dict[str, Any]
    ) -> None:
        """推进到下一个工艺路线"""
        current_route = completion_instance.order_craft_route
        
        # 查找下一个工艺路线
        next_route = await self.order_craft_route_repository.get_next_route_in_craft(
            current_route.order_craft_id, current_route.order, session
        )
        
        if next_route:
            # 启动下一个工艺路线
            next_route.start_route()
            await self.order_craft_route_repository.update(next_route)
            result["advanced_stages"].append(
                f"Advanced from {current_route.code} to {next_route.code}"
            )
        else:
            # 整个工艺完成
            order_craft = current_route.order_craft
            order_craft.complete_craft()
            # 这里需要注入order_craft_repository
            result["advanced_stages"].append(f"OrderCraft {order_craft.craft_code} completed")
    
    async def _update_craft_current_route(
        self,
        order_craft,
        session: AsyncSession,
        result: Dict[str, Any]
    ) -> None:
        """更新craft的当前工艺路线为最小的pending工艺路线"""
        try:
            # 获取该craft下所有的工艺路线，按order排序
            stmt = (
                select(OrderCraftRoute)
                .where(OrderCraftRoute.order_craft_id == order_craft.id)
                .where(OrderCraftRoute.status == "pending")
                .order_by(OrderCraftRoute.order)
            )
            db_result = await session.execute(stmt)
            pending_routes = db_result.scalars().all()
            
            if pending_routes:
                # 找到最小order的pending工艺路线
                min_pending_route = pending_routes[0]
                logger.info(f"Found min pending craft route {min_pending_route.code} for craft {order_craft.craft_code}")
                
                # 根据完成粒度更新相应实体的current_craft_route_code
                # 这里需要根据实际业务需求确定更新哪个级别的实体
                # 暂时先记录日志，具体实现需要根据业务逻辑确定
                result["updated_entities"].append(f"Set craft {order_craft.craft_code} current route to {min_pending_route.code}")
            else:
                # 没有pending的工艺路线，说明craft可能已经完成
                logger.info(f"No pending routes found for craft {order_craft.craft_code}")
                
        except Exception as e:
            logger.error(f"Failed to update craft current route: {str(e)}", exc_info=True)
            raise

    async def _update_parent_entity_status(
        self, 
        completion_instance: OrderCraftRouteInstance,
        session: AsyncSession,
        result: Dict[str, Any]
    ) -> None:
        """更新父级实体状态"""
        # 这里可以添加额外的父级状态更新逻辑
        # 例如：更新订单行状态、更新订单整体进度等
        pass
    
    async def get_completion_progress(
        self, 
        order_craft_route_id: int,
        granularity: CompletionGranularity,
        target_identifier: str,
        session: AsyncSession
    ) -> Dict[str, Any]:
        """获取完成进度"""
        # 查询指定范围内的完成实例
        progress_data = {
            "total_quantity": 0,
            "completed_quantity": 0,
            "completion_percentage": 0.0,
            "completion_instances": []
        }
        
        # 实现具体的进度查询逻辑
        return progress_data
    
    async def update_status_by_order_no(
        self, 
        order_no: str,
        factory_id: int,
        session: AsyncSession
    ) -> Dict[str, Any]:
        """
        根据订单号更新相关状态
        按照详细算法步骤进行状态更新：
        0. collect order_parts_array
        1-12. 按步骤进行状态更新
        """
        result = {
            "success": True,
            "message": "Status update completed successfully",
            "updated_entities": [],
            "errors": []
        }
        
        try:
            logger.info(f"Starting status update for order {order_no}")
            
            # Step 0: collect order_parts_array
            order_parts_array = await self._collect_order_parts_array(order_no, factory_id, session)
            if not order_parts_array:
                result["message"] = "No order parts found for the given order"
                return result
            
            logger.info(f"Collected {len(order_parts_array)} order parts for order {order_no}")
            
            # Get all crafts for this order
            crafts = await self._get_order_crafts(order_no, factory_id, session)
            
            # Process each craft
            for craft in crafts:
                await self._process_craft_status(craft, order_parts_array, session, result)
            
            # Step 12: check order status
            await self._check_and_update_order_status(order_no, factory_id, crafts, session, result)
            
            logger.info(f"Status update completed for order {order_no}")
            
        except Exception as e:
            logger.error(f"Error updating status for order {order_no}: {str(e)}", exc_info=True)
            result["success"] = False
            result["message"] = f"Status update failed: {str(e)}"
            result["errors"].append(str(e))
        
        return result
    
    async def _collect_order_parts_array(
        self, 
        order_no: str, 
        factory_id: int, 
        session: AsyncSession
    ) -> List[OrderPart]:
        """Step 0: collect order_parts_array"""
        try:
            parts = await self.order_part_repository.get_by_order_no_and_factory(
                order_no, factory_id, session
            )
            return parts
        except Exception as e:
            logger.error(f"Failed to collect order parts for order {order_no}: {str(e)}")
            return []
    
    async def _get_order_crafts(
        self, 
        order_no: str, 
        factory_id: int, 
        session: AsyncSession
    ) -> List[OrderCraft]:
        """Get all crafts for an order"""
        try:
            stmt = (
                select(OrderCraft)
                .options(
                    selectinload(OrderCraft.order_craft_routes)
                )
                .where(
                    and_(
                        OrderCraft.order_no == order_no,
                        OrderCraft.factory_id == factory_id
                    )
                )
                .order_by(OrderCraft.order)
            )
            result = await session.execute(stmt)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Failed to get order crafts for order {order_no}: {str(e)}")
            return []
    
    async def _process_craft_status(
        self,
        craft: OrderCraft,
        order_parts_array: List[OrderPart],
        session: AsyncSession,
        result: Dict[str, Any]
    ) -> None:
        """Process craft status according to algorithm steps 1-11"""
        try:
            logger.info(f"Processing craft {craft.craft_code} for order {craft.order_no}")
            
            # Steps 1-2: iterate through craft and craft_routes
            craft_route_statuses = []
            
            for craft_route in craft.order_craft_routes:
                # Step 3: init is_order_completed, completed_parts, completed_part_bundles
                is_order_completed = False
                completed_parts: Set[str] = set()
                completed_part_bundles: Set[str] = set()
                
                # Get all instances for this craft route
                craft_route_instances = await self.instance_repository.get_by_craft_route(
                    craft_route.id, session
                )
                
                # Step 4: is_pending = len(craft_route_instances) > 0
                is_pending = len(craft_route_instances) > 0
                
                # Step 5-6: min_start_time, max_end_time
                min_start_time = None
                max_end_time = None
                
                if craft_route_instances:
                    started_instances = [i for i in craft_route_instances if i.started_at]
                    completed_instances = [i for i in craft_route_instances if i.completed_at]
                    
                    if started_instances:
                        min_start_time = min(i.started_at for i in started_instances if i.started_at)
                    if completed_instances:
                        max_end_time = max(i.completed_at for i in completed_instances if i.completed_at)
                
                # Step 7: iterate craft_route_instances
                for instance in craft_route_instances:
                    if instance.status == "completed":
                        # Update completed_parts, completed_part_bundles
                        if instance.order_part_nos:
                            for part_no in instance.order_part_nos:
                                completed_parts.add(part_no)
                        
                        if instance.order_bundle_nos:
                            for bundle_no in instance.order_bundle_nos:
                                completed_part_bundles.add(bundle_no)
                        
                        # Check if all bundles for parts are completed
                        await self._check_part_completion(
                            instance, order_parts_array, completed_parts, 
                            completed_part_bundles, session
                        )
                
                # Step 8: check is_order_completed
                all_order_parts = {part.order_part_no for part in order_parts_array}
                is_order_completed = completed_parts == all_order_parts
                
                # Step 9: check craft_route_status
                craft_route_status = await self._determine_craft_route_status(
                    craft_route, is_order_completed, completed_parts, 
                    all_order_parts, is_pending, session
                )
                
                craft_route_statuses.append(craft_route_status)
                
                # Update craft route status if needed
                if craft_route.status != craft_route_status:
                    craft_route.status = craft_route_status
                    if craft_route_status == "completed":
                        craft_route.completed_at = datetime.now(timezone.utc)
                    elif craft_route_status == "in_progress":
                        if not craft_route.started_at:
                            craft_route.started_at = datetime.now(timezone.utc)
                    
                    result["updated_entities"].append(f"CraftRoute {craft_route.code}: {craft_route_status}")
            
            # Steps 10-11: update craft_status based on craft_route_status
            new_craft_status = await self._determine_craft_status(craft_route_statuses)
            
            if craft.status != new_craft_status:
                craft.status = new_craft_status
                if new_craft_status == "completed":
                    craft.completed_at = datetime.now(timezone.utc)
                elif new_craft_status == "in_progress":
                    if not craft.started_at:
                        craft.started_at = datetime.now(timezone.utc)
                
                result["updated_entities"].append(f"Craft {craft.craft_code}: {new_craft_status}")
                
        except Exception as e:
            logger.error(f"Failed to process craft status for {craft.craft_code}: {str(e)}")
            result["errors"].append(f"Craft {craft.craft_code}: {str(e)}")
    
    async def _check_part_completion(
        self,
        instance: OrderCraftRouteInstance,
        order_parts_array: List[OrderPart],
        completed_parts: Set[str],
        completed_part_bundles: Set[str],
        session: AsyncSession
    ) -> None:
        """Check if parts are completed based on bundle completion"""
        try:
            for part in order_parts_array:
                if part.order_part_no not in completed_parts:
                    # Get all bundles for this part
                    part_bundles = await self.order_bundle_repository.get_by_order_part_no_and_factory(
                        part.order_part_no, instance.factory_id, instance.order_no, session
                    )
                    
                    part_bundle_nos = {bundle.order_bundle_no for bundle in part_bundles}
                    
                    # Check if all bundles for this part are completed
                    if part_bundle_nos.issubset(completed_part_bundles):
                        completed_parts.add(part.order_part_no)
        except Exception as e:
            logger.error(f"Failed to check part completion: {str(e)}")
    
    async def _determine_craft_route_status(
        self,
        craft_route: OrderCraftRoute,
        is_order_completed: bool,
        completed_parts: Set[str],
        all_order_parts: Set[str],
        is_pending: bool,
        session: AsyncSession
    ) -> str:
        """Determine craft route status based on completion conditions"""
        # Step 9.1: if is_order_completed, then craft_route completed
        if is_order_completed:
            return "completed"
        
        # Step 9.2: if completed_parts = all_order_parts, then order_part completed
        if completed_parts == all_order_parts:
            return "completed"
        
        # If there are instances and some progress, it's in progress
        if is_pending and completed_parts:
            return "in_progress"
        
        # If there are instances but no progress, it's in progress
        if is_pending:
            return "in_progress"
        
        # Default to pending
        return "pending"
    
    async def _determine_craft_status(self, craft_route_statuses: List[str]) -> str:
        """Determine craft status based on craft route statuses"""
        if not craft_route_statuses:
            return "pending"
        
        # Step 10: if some craft_route_status is pending, then craft_status = pending
        if "pending" in craft_route_statuses or "in_progress" in craft_route_statuses:
            return "in_progress" if "in_progress" in craft_route_statuses else "pending"
        
        # Step 11: if all craft_route_status is finished, then craft_status = finished
        if all(status == "completed" for status in craft_route_statuses):
            return "completed"
        
        return "in_progress"
    
    async def _check_and_update_order_status(
        self,
        order_no: str,
        factory_id: int,
        crafts: List[OrderCraft],
        session: AsyncSession,
        result: Dict[str, Any]
    ) -> None:
        """Step 12: check and update order status"""
        try:
            if not crafts:
                return
            
            # Determine order status based on craft statuses
            craft_statuses = [craft.status for craft in crafts]
            
            new_order_status = OrderStatus.PENDING
            if all(status == "completed" for status in craft_statuses):
                new_order_status = OrderStatus.COMPLETED
            elif any(status in ["in_progress", "completed"] for status in craft_statuses):
                new_order_status = OrderStatus.IN_PROGRESS
            
            # Get and update order
            order = await self.order_repository.get_by_order_no_and_factory(
                order_no, factory_id, session
            )
            
            if order and order.status != new_order_status:
                order.status = new_order_status
                if new_order_status == OrderStatus.COMPLETED:
                    order.finished_at = datetime.now(timezone.utc)
                elif new_order_status == OrderStatus.IN_PROGRESS:
                    if not order.started_at:
                        order.started_at = datetime.now(timezone.utc)
                
                await self.order_repository.update(order)
                result["updated_entities"].append(f"Order {order_no}: {new_order_status}")
                
        except Exception as e:
            logger.error(f"Failed to update order status for {order_no}: {str(e)}")
            result["errors"].append(f"Order {order_no}: {str(e)}")