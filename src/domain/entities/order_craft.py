from datetime import datetime
from typing import Optional, TYPE_CHECKING, List
from enum import Enum
from sqlalchemy import String, Integer, DateTime, ForeignKey, Text, Boolean, Enum as SQLEnum
from sqlalchemy.orm import Mapped, mapped_column, relationship
from .base import Base

if TYPE_CHECKING:
    from .order import Order
    from .craft import Craft
    from .order_craft_route import OrderCraftRoute
    from .order_part import PartType


class OrderCraft(Base):
    """OrderCraft entity representing crafts assigned to a specific order."""
    
    __tablename__ = "order_crafts"
    
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    
    # Order and craft information
    order_no: Mapped[str] = mapped_column(String(100), ForeignKey("orders.order_no"), index=True)
    order_id: Mapped[int] = mapped_column(Integer, ForeignKey("orders.id"), index=True, comment="Order ID this craft belongs to")
    craft_code: Mapped[str] = mapped_column(String(50), ForeignKey("crafts.code"), index=True)
    craft_name: Mapped[Optional[str]] = mapped_column(String(100), comment="Craft name for easy reference")

    factory_id: Mapped[int] = mapped_column(ForeignKey("factories.id"), index=True, comment="Factory ID this craft belongs to")
    
    # Part type information
    part_type: Mapped[str] = mapped_column(String(50), index=True, comment="部位类型，对应PartType枚举值")
    
    # Workflow configuration
    order: Mapped[int] = mapped_column(Integer, default=0, index=True, comment="Order sequence in the workflow")
    is_required: Mapped[bool] = mapped_column(Boolean, default=True, comment="Whether this craft is required")
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, comment="Whether this craft is currently active")
    
    # Status and progress
    status: Mapped[str] = mapped_column(String(20), default="pending", index=True, comment="Status: pending, in_progress, completed, skipped")
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="When this craft started")
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="When this craft was completed")
    
    # Additional configuration
    notes: Mapped[Optional[str]] = mapped_column(Text, comment="Notes specific to this order-craft assignment")
    estimated_duration_hours: Mapped[Optional[int]] = mapped_column(Integer, comment="Estimated duration in hours")
    actual_duration_hours: Mapped[Optional[int]] = mapped_column(Integer, comment="Actual duration in hours")
    
    # Audit fields
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    order_entity: Mapped["Order"] = relationship("Order", foreign_keys=[order_no])
    craft: Mapped["Craft"] = relationship("Craft", foreign_keys=[craft_code])
    order_craft_routes: Mapped[List["OrderCraftRoute"]] = relationship(
        "OrderCraftRoute", 
        back_populates="order_craft", 
        cascade="all, delete-orphan",
        order_by="OrderCraftRoute.order"
    )
    
    def __repr__(self) -> str:
        return f"<OrderCraft(order_no='{self.order_no}', craft_code='{self.craft_code}', part_type='{self.part_type}', order={self.order}, status='{self.status}')>"
    
    def start_craft(self) -> None:
        """Start this craft for the order."""
        if self.status == "pending":
            self.status = "in_progress"
            self.started_at = datetime.utcnow()
            self.updated_at = datetime.utcnow()
    
    def complete_craft(self) -> None:
        """Complete this craft for the order."""
        if self.status == "in_progress":
            self.status = "completed"
            self.completed_at = datetime.utcnow()
            self.updated_at = datetime.utcnow()
            
            # Calculate actual duration if we have start time
            if self.started_at:
                duration = self.completed_at - self.started_at
                self.actual_duration_hours = int(duration.total_seconds() / 3600)
    
    def skip_craft(self, reason: Optional[str] = None) -> None:
        """Skip this craft for the order."""
        self.status = "skipped"
        self.completed_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        if reason:
            self.notes = f"{self.notes or ''}\nSkipped: {reason}".strip()
    
    def reset_craft(self) -> None:
        """Reset this craft back to pending status."""
        self.status = "pending"
        self.started_at = None
        self.completed_at = None
        self.actual_duration_hours = None
        self.updated_at = datetime.utcnow()
    
    def is_completed(self) -> bool:
        """Check if this craft is completed."""
        return self.status == "completed"
    
    def is_in_progress(self) -> bool:
        """Check if this craft is in progress."""
        return self.status == "in_progress"
    
    def is_pending(self) -> bool:
        """Check if this craft is pending."""
        return self.status == "pending"
    
    def can_start(self) -> bool:
        """Check if this craft can be started."""
        return self.is_active and self.status == "pending"
    
    def get_completion_percentage(self) -> float:
        """Get completion percentage based on order craft routes."""
        if not self.order_craft_routes:
            return 100.0 if self.is_completed() else 0.0
        
        completed_routes = sum(1 for route in self.order_craft_routes if route.is_completed())
        total_routes = len(self.order_craft_routes)
        
        return (completed_routes / total_routes) * 100.0 if total_routes > 0 else 0.0
    
    def get_part_type_enum(self) -> "PartType":
        """Get the PartType enum for this order craft."""
        from .order_part import PartType
        return PartType(self.part_type)
    
    def set_part_type(self, part_type: "PartType") -> None:
        """Set the part type for this order craft."""
        self.part_type = part_type.value
        self.updated_at = datetime.utcnow()
    
    def validate_part_type_consistency(self) -> bool:
        """Validate that the part type is consistent with the order's part types."""
        if not self.order_entity or not self.order_entity.part_types:
            return True  # 如果订单没有指定part_types，则不验证
        
        return self.part_type in self.order_entity.part_types