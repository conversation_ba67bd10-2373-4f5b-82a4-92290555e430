from datetime import datetime, timezone
from typing import Optional, TYPE_CHECKING, List
from enum import Enum
from sqlalchemy import String, Integer, DateTime, ForeignKey, Text, Boolean, Enum as SQLEnum, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship
from .base import Base

if TYPE_CHECKING:
    from .order import Order
    from .order_part import OrderPart, PartType
    from .order_craft import OrderCraft
    from .order_craft_route_instance import OrderCraftRouteInstance
    from .user import User


class OrderCraftInstanceStatus(Enum):
    """OrderCraftInstance status enumeration."""
    PENDING = "pending"         # 待开始
    IN_PROGRESS = "in_progress" # 进行中
    COMPLETED = "completed"     # 已完成
    ON_HOLD = "on_hold"        # 暂停
    SKIPPED = "skipped"        # 跳过
    FAILED = "failed"          # 失败


class OrderCraftInstance(Base):
    """OrderCraftInstance entity representing craft execution for a specific part."""
    
    __tablename__ = "order_craft_instances"
    __table_args__ = (
        UniqueConstraint('order_no', 'order_part_no', 'craft_code', 'factory_id', name='uq_order_craft_instance_part_craft'),
    )
    
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    
    # Core identification fields
    factory_id: Mapped[int] = mapped_column(ForeignKey("factories.id"), index=True, comment="工厂ID")
    order_no: Mapped[str] = mapped_column(String(100), index=True, comment="订单号")
    order_part_no: Mapped[str] = mapped_column(String(100), index=True, comment="订单部位号")
    part_type: Mapped[str] = mapped_column(String(50), index=True, comment="部位类型，对应PartType枚举值")
    craft_code: Mapped[str] = mapped_column(String(50), index=True, comment="工艺代码")
    
    # Foreign key relationships
    order_id: Mapped[int] = mapped_column(ForeignKey("orders.id"), index=True, comment="订单ID")
    order_part_id: Mapped[int] = mapped_column(ForeignKey("order_parts.id"), index=True, comment="订单部位ID")
    order_craft_id: Mapped[int] = mapped_column(ForeignKey("order_crafts.id"), index=True, comment="订单工艺ID")
    
    # Status and progress tracking
    status: Mapped[OrderCraftInstanceStatus] = mapped_column(SQLEnum(OrderCraftInstanceStatus), default=OrderCraftInstanceStatus.PENDING, index=True, comment="实例状态")
    progress_percentage: Mapped[int] = mapped_column(Integer, default=0, comment="完成百分比 0-100")
    
    # Workflow configuration
    sequence_order: Mapped[int] = mapped_column(Integer, default=0, comment="在部位工艺流程中的顺序")
    is_required: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否必需的工艺步骤")
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否激活")
    
    # Timing information
    planned_start_date: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="计划开始时间")
    planned_end_date: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="计划完成时间")
    actual_start_date: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="实际开始时间")
    actual_end_date: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="实际完成时间")
    
    # Quality and performance tracking
    quality_score: Mapped[Optional[int]] = mapped_column(Integer, comment="质量分数 0-100")
    rework_count: Mapped[int] = mapped_column(Integer, default=0, comment="返工次数")
    defect_count: Mapped[int] = mapped_column(Integer, default=0, comment="次品数量")
    
    # Assignment and responsibility
    assigned_user_id: Mapped[Optional[int]] = mapped_column(ForeignKey("users.id"), comment="分配的用户ID")
    supervisor_user_id: Mapped[Optional[int]] = mapped_column(ForeignKey("users.id"), comment="负责人用户ID")
    
    # Duration tracking
    estimated_duration_minutes: Mapped[Optional[int]] = mapped_column(Integer, comment="预估耗时(分钟)")
    actual_duration_minutes: Mapped[Optional[int]] = mapped_column(Integer, comment="实际耗时(分钟)")
    
    # Notes and additional information
    notes: Mapped[Optional[str]] = mapped_column(Text, comment="备注")
    completion_notes: Mapped[Optional[str]] = mapped_column(Text, comment="完成备注")
    
    # Audit fields
    created_at: Mapped[datetime] = mapped_column(DateTime, default=lambda: datetime.now(timezone.utc), comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc), comment="更新时间")
    
    # Relationships - commented out to avoid circular import issues during initial deployment
    # These can be enabled later once all entities are properly integrated
    # order: Mapped["Order"] = relationship("Order", foreign_keys=[order_id], lazy="select")
    # order_part: Mapped["OrderPart"] = relationship("OrderPart", foreign_keys=[order_part_id], lazy="select")
    # order_craft: Mapped["OrderCraft"] = relationship("OrderCraft", foreign_keys=[order_craft_id], lazy="select")
    # assigned_user: Mapped[Optional["User"]] = relationship("User", foreign_keys=[assigned_user_id], lazy="select")
    # supervisor: Mapped[Optional["User"]] = relationship("User", foreign_keys=[supervisor_user_id], lazy="select")
    
    # Remove the direct relationship for now to avoid circular import issues
    # order_craft_route_instances: Mapped[List["OrderCraftRouteInstance"]] = relationship(
    #     "OrderCraftRouteInstance",
    #     primaryjoin="OrderCraftInstance.id == foreign(OrderCraftRouteInstance.order_craft_instance_id)",
    #     cascade="all, delete-orphan",
    #     lazy="select"
    # )
    
    def __repr__(self) -> str:
        return f"<OrderCraftInstance(order_no='{self.order_no}', part_no='{self.order_part_no}', craft='{self.craft_code}', status='{self.status.value}')>"
    
    # Status management methods
    def start_instance(self) -> None:
        """Start this craft instance."""
        if self.status == OrderCraftInstanceStatus.PENDING:
            self.status = OrderCraftInstanceStatus.IN_PROGRESS
            self.actual_start_date = datetime.now(timezone.utc)
            self.updated_at = datetime.now(timezone.utc)
    
    def complete_instance(self, completion_notes: Optional[str] = None) -> None:
        """Complete this craft instance."""
        if self.status == OrderCraftInstanceStatus.IN_PROGRESS:
            self.status = OrderCraftInstanceStatus.COMPLETED
            self.actual_end_date = datetime.now(timezone.utc)
            self.progress_percentage = 100
            self.updated_at = datetime.now(timezone.utc)
            
            if completion_notes:
                self.completion_notes = completion_notes
                
            # Calculate actual duration if we have start time
            if self.actual_start_date:
                duration = self.actual_end_date - self.actual_start_date
                self.actual_duration_minutes = int(duration.total_seconds() / 60)
    
    def hold_instance(self, reason: Optional[str] = None) -> None:
        """Put this craft instance on hold."""
        if self.status == OrderCraftInstanceStatus.IN_PROGRESS:
            self.status = OrderCraftInstanceStatus.ON_HOLD
            self.updated_at = datetime.now(timezone.utc)
            if reason:
                self.notes = f"{self.notes or ''}\nOn Hold: {reason}".strip()
    
    def resume_instance(self) -> None:
        """Resume this craft instance from hold."""
        if self.status == OrderCraftInstanceStatus.ON_HOLD:
            self.status = OrderCraftInstanceStatus.IN_PROGRESS
            self.updated_at = datetime.now(timezone.utc)
    
    def skip_instance(self, reason: Optional[str] = None) -> None:
        """Skip this craft instance."""
        self.status = OrderCraftInstanceStatus.SKIPPED
        self.actual_end_date = datetime.now(timezone.utc)
        self.updated_at = datetime.now(timezone.utc)
        if reason:
            self.notes = f"{self.notes or ''}\nSkipped: {reason}".strip()
    
    def fail_instance(self, reason: Optional[str] = None) -> None:
        """Mark this craft instance as failed."""
        self.status = OrderCraftInstanceStatus.FAILED
        self.actual_end_date = datetime.now(timezone.utc)
        self.updated_at = datetime.now(timezone.utc)
        if reason:
            self.notes = f"{self.notes or ''}\nFailed: {reason}".strip()
    
    def reset_instance(self) -> None:
        """Reset this craft instance back to pending status."""
        self.status = OrderCraftInstanceStatus.PENDING
        self.actual_start_date = None
        self.actual_end_date = None
        self.progress_percentage = 0
        self.actual_duration_minutes = None
        self.updated_at = datetime.now(timezone.utc)
    
    # Status check methods
    def is_pending(self) -> bool:
        """Check if instance is pending."""
        return self.status == OrderCraftInstanceStatus.PENDING
    
    def is_in_progress(self) -> bool:
        """Check if instance is in progress."""
        return self.status == OrderCraftInstanceStatus.IN_PROGRESS
    
    def is_completed(self) -> bool:
        """Check if instance is completed."""
        return self.status == OrderCraftInstanceStatus.COMPLETED
    
    def is_on_hold(self) -> bool:
        """Check if instance is on hold."""
        return self.status == OrderCraftInstanceStatus.ON_HOLD
    
    def is_skipped(self) -> bool:
        """Check if instance is skipped."""
        return self.status == OrderCraftInstanceStatus.SKIPPED
    
    def is_failed(self) -> bool:
        """Check if instance is failed."""
        return self.status == OrderCraftInstanceStatus.FAILED
    
    def can_start(self) -> bool:
        """Check if this craft instance can be started."""
        return self.is_active and self.status == OrderCraftInstanceStatus.PENDING
    
    def can_complete(self) -> bool:
        """Check if this craft instance can be completed."""
        return self.status == OrderCraftInstanceStatus.IN_PROGRESS
    
    # Progress and quality management
    def update_progress(self, percentage: int) -> None:
        """Update progress percentage (0-100)."""
        if 0 <= percentage <= 100:
            self.progress_percentage = percentage
            self.updated_at = datetime.now(timezone.utc)
            
            if percentage == 100 and self.status == OrderCraftInstanceStatus.IN_PROGRESS:
                self.complete_instance()
    
    def add_rework(self, reason: Optional[str] = None) -> None:
        """Record a rework incident."""
        self.rework_count += 1
        self.updated_at = datetime.now(timezone.utc)
        if reason:
            self.notes = f"{self.notes or ''}\nRework #{self.rework_count}: {reason}".strip()
    
    def add_defect(self, count: int = 1, reason: Optional[str] = None) -> None:
        """Record defect(s)."""
        self.defect_count += count
        self.updated_at = datetime.now(timezone.utc)
        if reason:
            self.notes = f"{self.notes or ''}\nDefects +{count}: {reason}".strip()
    
    def set_quality_score(self, score: int) -> None:
        """Set quality score (0-100)."""
        if 0 <= score <= 100:
            self.quality_score = score
            self.updated_at = datetime.now(timezone.utc)
    
    # Assignment methods
    def assign_to_user(self, user_id: int) -> None:
        """Assign this craft instance to a user."""
        self.assigned_user_id = user_id
        self.updated_at = datetime.now(timezone.utc)
    
    def set_supervisor(self, user_id: int) -> None:
        """Set supervisor for this craft instance."""
        self.supervisor_user_id = user_id
        self.updated_at = datetime.now(timezone.utc)
    
    # Part type management
    def get_part_type_enum(self) -> "PartType":
        """Get the PartType enum for this craft instance."""
        from .order_part import PartType
        return PartType(self.part_type)
    
    def set_part_type(self, part_type: "PartType") -> None:
        """Set the part type for this craft instance."""
        self.part_type = part_type.value
        self.updated_at = datetime.now(timezone.utc)
    
    def validate_part_type_consistency(self) -> bool:
        """Validate that the part type is consistent with the order part."""
        # TODO: Implement this method by querying the database
        # For now, return True to avoid relationship issues
        return True
    
    # Route instance management
    def get_completed_route_instances_count(self) -> int:
        """Get count of completed route instances."""
        # TODO: Implement this method by querying the database
        # For now, return 0 to avoid relationship issues
        return 0
    
    def get_total_route_instances_count(self) -> int:
        """Get total count of route instances."""
        # TODO: Implement this method by querying the database
        # For now, return 0 to avoid relationship issues
        return 0
    
    def calculate_route_completion_percentage(self) -> float:
        """Calculate completion percentage based on route instances."""
        total = self.get_total_route_instances_count()
        if total == 0:
            return 0.0
        
        completed = self.get_completed_route_instances_count()
        return (completed / total) * 100.0
    
    def get_duration_minutes(self) -> Optional[int]:
        """Get actual duration in minutes if both start and end times exist."""
        if self.actual_start_date and self.actual_end_date:
            duration = self.actual_end_date - self.actual_start_date
            return int(duration.total_seconds() / 60)
        return self.actual_duration_minutes
    
    def is_overdue(self) -> bool:
        """Check if this instance is overdue based on planned end date."""
        if not self.planned_end_date:
            return False
        
        if self.is_completed():
            return self.actual_end_date > self.planned_end_date if self.actual_end_date else False
        
        return datetime.now(timezone.utc) > self.planned_end_date
    
    @classmethod
    def generate_craft_instance_key(cls, order_no: str, order_part_no: str, craft_code: str) -> str:
        """Generate a unique key for this craft instance."""
        return f"{order_no}_{order_part_no}_{craft_code}"