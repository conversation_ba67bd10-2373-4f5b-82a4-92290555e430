from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from enum import Enum
from sqlalchemy import String, Integer, DateTime, ForeignKey, Text, JSON, Boolean, Index
from sqlalchemy.orm import Mapped, mapped_column
from .base import Base


class TemplateStatus(Enum):
    """Template status enumeration."""
    ACTIVE = "active"           # 活跃
    INACTIVE = "inactive"       # 非活跃
    DRAFT = "draft"            # 草稿
    ARCHIVED = "archived"      # 已归档


class CreateTemplate(Base):
    """CreateTemplate entity for storing template data with JSON fields."""
    
    __tablename__ = "create_templates"
    __table_args__ = (
        Index('ix_create_template_search', 'part_type', 'template_name', 'template_code'),
        Index('ix_create_template_part_type_status', 'part_type', 'status'),
    )
    
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    
    # Core identification fields for search
    factory_id: Mapped[int] = mapped_column(ForeignKey("factories.id"), index=True, comment="工厂ID")
    part_type: Mapped[str] = mapped_column(String(50), index=True, comment="部位类型")
    template_name: Mapped[str] = mapped_column(String(100), index=True, comment="模板名称")
    template_code: Mapped[str] = mapped_column(String(50), index=True, comment="模板代码")
    
    # Template metadata
    version: Mapped[str] = mapped_column(String(20), default="1.0.0", comment="模板版本")
    description: Mapped[Optional[str]] = mapped_column(Text, comment="模板描述")
    status: Mapped[TemplateStatus] = mapped_column(String(20), default=TemplateStatus.ACTIVE, index=True, comment="模板状态")
    
    # JSON data storage - main template content
    template_data: Mapped[Dict[str, Any]] = mapped_column(JSON, comment="模板主要数据(JSON格式)")
    
    # Additional JSON fields for flexibility
    configuration: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, comment="配置数据")
    metadata: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, comment="元数据")
    validation_rules: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, comment="验证规则")
    ui_config: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, comment="UI配置")
    
    # Template hierarchy and relationships
    parent_template_id: Mapped[Optional[int]] = mapped_column(ForeignKey("create_templates.id"), comment="父模板ID")
    category: Mapped[Optional[str]] = mapped_column(String(50), index=True, comment="模板分类")
    tags: Mapped[Optional[List[str]]] = mapped_column(JSON, comment="标签列表")
    
    # Usage and access control
    is_public: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否公开")
    is_system_template: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否系统模板")
    access_level: Mapped[str] = mapped_column(String(20), default="standard", comment="访问级别: standard, restricted, admin")
    
    # Creator and ownership
    created_by_user_id: Mapped[int] = mapped_column(ForeignKey("users.id"), index=True, comment="创建者用户ID")
    updated_by_user_id: Mapped[Optional[int]] = mapped_column(ForeignKey("users.id"), comment="最后更新者用户ID")
    
    # Usage statistics
    usage_count: Mapped[int] = mapped_column(Integer, default=0, comment="使用次数")
    last_used_at: Mapped[Optional[datetime]] = mapped_column(DateTime, comment="最后使用时间")
    
    # Audit fields
    created_at: Mapped[datetime] = mapped_column(DateTime, default=lambda: datetime.now(timezone.utc), comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc), comment="更新时间")
    
    # Relationships - commented out to avoid circular import issues
    # parent_template: Mapped[Optional["CreateTemplate"]] = relationship("CreateTemplate", remote_side=[id])
    # child_templates: Mapped[List["CreateTemplate"]] = relationship("CreateTemplate", back_populates="parent_template")
    # created_by: Mapped["User"] = relationship("User", foreign_keys=[created_by_user_id])
    # updated_by: Mapped[Optional["User"]] = relationship("User", foreign_keys=[updated_by_user_id])
    
    def __repr__(self) -> str:
        return f"<CreateTemplate(code='{self.template_code}', name='{self.template_name}', part_type='{self.part_type}')>"
    
    # Template data management methods
    def get_template_data(self) -> Dict[str, Any]:
        """Get the template data."""
        return self.template_data or {}
    
    def set_template_data(self, data: Dict[str, Any]) -> None:
        """Set the template data."""
        self.template_data = data
        self.updated_at = datetime.now(timezone.utc)
    
    def update_template_data(self, updates: Dict[str, Any]) -> None:
        """Update specific fields in template data."""
        current_data = self.get_template_data()
        current_data.update(updates)
        self.set_template_data(current_data)
    
    # Configuration management
    def get_configuration(self) -> Dict[str, Any]:
        """Get configuration data."""
        return self.configuration or {}
    
    def set_configuration(self, config: Dict[str, Any]) -> None:
        """Set configuration data."""
        self.configuration = config
        self.updated_at = datetime.now(timezone.utc)
    
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """Get a specific configuration value."""
        return self.get_configuration().get(key, default)
    
    def set_config_value(self, key: str, value: Any) -> None:
        """Set a specific configuration value."""
        config = self.get_configuration()
        config[key] = value
        self.set_configuration(config)
    
    # Metadata management
    def get_metadata(self) -> Dict[str, Any]:
        """Get metadata."""
        return self.metadata or {}
    
    def set_metadata(self, metadata: Dict[str, Any]) -> None:
        """Set metadata."""
        self.metadata = metadata
        self.updated_at = datetime.now(timezone.utc)
    
    def add_metadata(self, key: str, value: Any) -> None:
        """Add a metadata field."""
        metadata = self.get_metadata()
        metadata[key] = value
        self.set_metadata(metadata)
    
    # Tag management
    def get_tags(self) -> List[str]:
        """Get tags list."""
        return self.tags or []
    
    def add_tag(self, tag: str) -> None:
        """Add a tag."""
        tags = self.get_tags()
        if tag not in tags:
            tags.append(tag)
            self.tags = tags
            self.updated_at = datetime.now(timezone.utc)
    
    def remove_tag(self, tag: str) -> None:
        """Remove a tag."""
        tags = self.get_tags()
        if tag in tags:
            tags.remove(tag)
            self.tags = tags
            self.updated_at = datetime.now(timezone.utc)
    
    def has_tag(self, tag: str) -> bool:
        """Check if template has a specific tag."""
        return tag in self.get_tags()
    
    # Status management
    def activate(self) -> None:
        """Activate the template."""
        self.status = TemplateStatus.ACTIVE
        self.updated_at = datetime.now(timezone.utc)
    
    def deactivate(self) -> None:
        """Deactivate the template."""
        self.status = TemplateStatus.INACTIVE
        self.updated_at = datetime.now(timezone.utc)
    
    def archive(self) -> None:
        """Archive the template."""
        self.status = TemplateStatus.ARCHIVED
        self.updated_at = datetime.now(timezone.utc)
    
    def set_as_draft(self) -> None:
        """Set template as draft."""
        self.status = TemplateStatus.DRAFT
        self.updated_at = datetime.now(timezone.utc)
    
    def is_active(self) -> bool:
        """Check if template is active."""
        return self.status == TemplateStatus.ACTIVE
    
    def is_draft(self) -> bool:
        """Check if template is draft."""
        return self.status == TemplateStatus.DRAFT
    
    def is_archived(self) -> bool:
        """Check if template is archived."""
        return self.status == TemplateStatus.ARCHIVED
    
    # Usage tracking
    def increment_usage(self) -> None:
        """Increment usage count and update last used time."""
        self.usage_count += 1
        self.last_used_at = datetime.now(timezone.utc)
        self.updated_at = datetime.now(timezone.utc)
    
    def reset_usage_stats(self) -> None:
        """Reset usage statistics."""
        self.usage_count = 0
        self.last_used_at = None
        self.updated_at = datetime.now(timezone.utc)
    
    # Validation methods
    def validate_template_data(self) -> bool:
        """Validate template data against validation rules."""
        if not self.validation_rules:
            return True
        
        # TODO: Implement validation logic based on validation_rules
        # For now, just check if template_data exists and is valid JSON
        return bool(self.template_data and isinstance(self.template_data, dict))
    
    def is_valid_for_part_type(self, part_type: str) -> bool:
        """Check if template is valid for a specific part type."""
        return self.part_type == part_type and self.is_active()
    
    # Access control methods
    def can_be_accessed_by_user(self, user_access_level: str) -> bool:
        """Check if user can access this template based on access level."""
        access_hierarchy = {
            "standard": 1,
            "restricted": 2,
            "admin": 3
        }
        
        user_level = access_hierarchy.get(user_access_level, 1)
        template_level = access_hierarchy.get(self.access_level, 1)
        
        return user_level >= template_level
    
    def set_access_level(self, level: str) -> None:
        """Set access level for the template."""
        valid_levels = ["standard", "restricted", "admin"]
        if level in valid_levels:
            self.access_level = level
            self.updated_at = datetime.now(timezone.utc)
    
    # Version management
    def increment_version(self) -> None:
        """Increment template version."""
        try:
            version_parts = self.version.split('.')
            if len(version_parts) == 3:
                patch = int(version_parts[2]) + 1
                self.version = f"{version_parts[0]}.{version_parts[1]}.{patch}"
            else:
                self.version = "1.0.1"
        except (ValueError, IndexError):
            self.version = "1.0.1"
        
        self.updated_at = datetime.now(timezone.utc)
    
    def set_version(self, version: str) -> None:
        """Set template version."""
        self.version = version
        self.updated_at = datetime.now(timezone.utc)
    
    # UI configuration management
    def get_ui_config(self) -> Dict[str, Any]:
        """Get UI configuration."""
        return self.ui_config or {}
    
    def set_ui_config(self, config: Dict[str, Any]) -> None:
        """Set UI configuration."""
        self.ui_config = config
        self.updated_at = datetime.now(timezone.utc)
    
    def get_ui_setting(self, key: str, default: Any = None) -> Any:
        """Get a specific UI setting."""
        return self.get_ui_config().get(key, default)
    
    # Search helper methods
    def matches_search_criteria(self, part_type: str = None, template_name: str = None, template_code: str = None) -> bool:
        """Check if template matches search criteria."""
        if part_type and self.part_type != part_type:
            return False
        if template_name and template_name.lower() not in self.template_name.lower():
            return False
        if template_code and template_code.lower() not in self.template_code.lower():
            return False
        return True
    
    # Template cloning
    def clone_template(self, new_name: str, new_code: str) -> "CreateTemplate":
        """Create a copy of this template with new name and code."""
        cloned = CreateTemplate(
            factory_id=self.factory_id,
            part_type=self.part_type,
            template_name=new_name,
            template_code=new_code,
            description=f"Cloned from {self.template_name}",
            template_data=self.template_data.copy() if self.template_data else {},
            configuration=self.configuration.copy() if self.configuration else None,
            metadata=self.metadata.copy() if self.metadata else None,
            validation_rules=self.validation_rules.copy() if self.validation_rules else None,
            ui_config=self.ui_config.copy() if self.ui_config else None,
            category=self.category,
            tags=self.tags.copy() if self.tags else None,
            is_public=self.is_public,
            access_level=self.access_level,
            created_by_user_id=self.created_by_user_id
        )
        return cloned