from datetime import datetime, timezone
from typing import List, Optional, TYPE_CHECKING
from enum import Enum
from sqlalchemy import String, Integer, DateTime, Text, Enum as SQLEnum, ForeignKey, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship
from .base import Base

if TYPE_CHECKING:
    from .order import Order
    from .user import User
    from .order_bundle import OrderBundle


class PartStatus(Enum):
    """部位生产状态枚举"""
    PLANNED = "planned"           # 计划中
    CUTTING = "cutting"           # 裁剪中
    SEWING = "sewing"            # 缝制中
    QUALITY_CHECK = "quality_check"  # 质检中
    COMPLETED = "completed"       # 已完成
    ON_HOLD = "on_hold"          # 暂停
    CANCELLED = "cancelled"       # 已取消


class PartType(Enum):
    """服装部位类型枚举"""
    FRONT_BODY = "front_body"         # 前身
    BACK_BODY = "back_body"           # 后身
    SLEEVE = "sleeve"                 # 袖子
    COLLAR = "collar"                 # 领子
    POCKET = "pocket"                 # 口袋
    CUFF = "cuff"                     # 袖口
    WAISTBAND = "waistband"           # 腰带
    LEG = "leg"                       # 裤腿
    ZIPPER = "zipper"                 # 拉链
    BUTTON_PLACKET = "button_placket" # 门襟
    LINING = "lining"                 # 里料
    ACCESSORIES = "accessories"       # 辅料
    OTHER = "other"                   # 其他


class OrderPart(Base):
    """订单部位实体 - 服装生产中的机床/部位单位"""
    
    __tablename__ = "order_parts"
    __table_args__ = (
        UniqueConstraint('order_part_no', 'factory_id', 'order_no', name='uq_order_part_no_factory_order'),
    )
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    
    # Factory and order relationship
    factory_id: Mapped[int] = mapped_column(ForeignKey("factories.id"), index=True, comment="工厂ID")
    order_id: Mapped[int] = mapped_column(ForeignKey("orders.id"), comment="订单ID")
    order_no: Mapped[str] = mapped_column(String(100), index=True, comment="订单号")
    
    # 基本信息
    order_part_no: Mapped[str] = mapped_column(String(100), index=True, nullable=False, comment="订单部位号")
    skc_no: Mapped[str] = mapped_column(String(100), nullable=False, comment="款号")
    color: Mapped[str] = mapped_column(String(50), nullable=False, comment="颜色")
    
    # 部位信息
    part_type: Mapped[PartType] = mapped_column(SQLEnum(PartType), nullable=False, comment="部位类型")
    part_name: Mapped[str] = mapped_column(String(100), nullable=False, comment="部位名称")
    part_sequence: Mapped[int] = mapped_column(Integer, nullable=False, comment="部位序号(同一订单内)")
    
    # 数量信息
    total_quantity: Mapped[int] = mapped_column(Integer, nullable=False, comment="部位总件数")
    completed_quantity: Mapped[int] = mapped_column(Integer, default=0, comment="已完成件数")
    
    # 状态和进度
    status: Mapped[PartStatus] = mapped_column(SQLEnum(PartStatus), default=PartStatus.PLANNED, comment="部位状态")
    current_craft_route_code: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, index=True, comment="当前工艺路线代码")
    progress_percentage: Mapped[int] = mapped_column(Integer, default=0, comment="完成百分比")
    
    # 时间信息
    planned_start_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="计划开始时间")
    actual_start_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="实际开始时间")
    planned_end_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="计划完成时间")
    actual_end_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="实际完成时间")
    
    # 机床和工序信息
    machine_no: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="机床编号")
    process_route: Mapped[Optional[str]] = mapped_column(String(200), nullable=True, comment="工序路线")
    
    # 描述和备注
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="部位描述")
    notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="备注")
    
    # 责任人
    supervisor_user_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("users.id"), nullable=True, comment="负责人用户ID")
    
    # 系统字段
    created_at: Mapped[datetime] = mapped_column(DateTime, default=lambda: datetime.now(timezone.utc), comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc), comment="更新时间")
    
    # 关系
    order: Mapped["Order"] = relationship("Order", back_populates="order_parts", foreign_keys=[order_id])
    supervisor: Mapped[Optional["User"]] = relationship("User", foreign_keys=[supervisor_user_id])
    order_bundles: Mapped[List["OrderBundle"]] = relationship("OrderBundle", back_populates="order_part", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        return f"<OrderPart(id={self.id}, order_part_no='{self.order_part_no}', order_no='{self.order_no}', part_type='{self.part_type.value}')>"
    
    @classmethod
    def generate_order_part_no(cls, order_no: str, part_sequence: int, part_type: PartType) -> str:
        """生成订单部位号: {订单号}_P{部位序号}_{部位类型}"""
        return f"{order_no}_P{part_sequence:02d}_{part_type.value.upper()}"
    
    def calculate_progress(self) -> int:
        """计算完成进度百分比"""
        if self.total_quantity == 0:
            return 0
        return min(100, int((self.completed_quantity / self.total_quantity) * 100))
    
    def update_progress(self) -> None:
        """更新进度百分比"""
        self.progress_percentage = self.calculate_progress()
    
    def is_completed(self) -> bool:
        """检查部位是否已完成"""
        return self.status == PartStatus.COMPLETED or self.completed_quantity >= self.total_quantity
    
    def can_start_production(self) -> bool:
        """检查是否可以开始生产"""
        return self.status in [PartStatus.PLANNED] and self.total_quantity > 0
    
    def start_production(self) -> None:
        """开始生产"""
        if self.can_start_production():
            self.status = PartStatus.CUTTING
            self.actual_start_date = datetime.now(timezone.utc)
    
    def complete_part(self) -> None:
        """完成部位生产"""
        self.status = PartStatus.COMPLETED
        self.actual_end_date = datetime.now(timezone.utc)
        self.completed_quantity = self.total_quantity
        self.update_progress()
    
    def get_bundles_by_size(self, size: str) -> List["OrderBundle"]:
        """获取指定尺码的所有扎"""
        return [bundle for bundle in self.order_bundles if bundle.size == size]
    
    def get_total_bundles_count(self) -> int:
        """获取总扎数"""
        return len(self.order_bundles)
    
    def get_size_distribution(self) -> dict:
        """获取尺码分布统计"""
        size_stats = {}
        for bundle in self.order_bundles:
            size = bundle.size
            if size not in size_stats:
                size_stats[size] = {
                    "bundles_count": 0,
                    "total_quantity": 0,
                    "completed_quantity": 0
                }
            size_stats[size]["bundles_count"] += 1
            size_stats[size]["total_quantity"] += bundle.quantity
            size_stats[size]["completed_quantity"] += bundle.completed_quantity
        
        return size_stats
    
    def validate_order_part_type_consistency(self) -> bool:
        """验证订单部位类型与订单要求的部位类型是否一致"""
        if not self.order or not self.order.part_types:
            return True  # 如果订单没有指定part_types，则不验证
        
        return self.part_type.value in self.order.part_types
    
    def ensure_part_type_in_order(self) -> None:
        """确保当前部位类型在订单的part_types中"""
        if self.order and not self.order.has_part_type(self.part_type):
            self.order.add_part_type(self.part_type)